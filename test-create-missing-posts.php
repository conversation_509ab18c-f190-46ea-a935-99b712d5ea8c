<?php
/**
 * Test script to create missing WordPress posts
 */

if (php_sapi_name() !== 'cli') {
    die("This script must be run from command line");
}

// Load WordPress
define('BASE_PATH', dirname(dirname(dirname(__DIR__))) . '/');
define('WP_USE_THEMES', false);
require(BASE_PATH . 'wp-load.php');

echo "=== Testing Missing WordPress Posts Creation ===\n";

try {
    // Check current status
    $api = NnaApi::getInstance();
    $collection = $api->getCollection($api->getDbName() . '.objects');
    
    $totalObjects = $api->getObjectsCount(['Metainfo.source' => BrenollisToScoroMapper::SOURCE]);
    $objectsWithoutPosts = $collection->count([
        'Metainfo.source' => BrenollisToScoroMapper::SOURCE,
        '$or' => [
            ['PostIds' => ['$exists' => false]],
            ['PostIds' => null],
            ['PostIds' => []]
        ]
    ]);
    
    $wpPosts = get_posts(['post_type' => 'object', 'numberposts' => -1, 'post_status' => 'any']);
    
    echo "Current Status:\n";
    echo "- Total MongoDB objects: $totalObjects\n";
    echo "- Objects without WordPress posts: $objectsWithoutPosts\n";
    echo "- Existing WordPress posts: " . count($wpPosts) . "\n\n";
    
    if ($objectsWithoutPosts > 0) {
        echo "Creating WordPress posts for $objectsWithoutPosts objects...\n\n";
        
        $result = BrenollisSync::getInstance()->createMissingWordPressPosts();
        
        echo "\nResults:\n";
        echo "- Created: " . $result['created'] . " posts\n";
        echo "- Errors: " . $result['errors'] . " errors\n";
        
        // Check final status
        $finalWpPosts = get_posts(['post_type' => 'object', 'numberposts' => -1, 'post_status' => 'any']);
        echo "- Final WordPress posts count: " . count($finalWpPosts) . "\n";
        
    } else {
        echo "✓ All objects already have WordPress posts!\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
