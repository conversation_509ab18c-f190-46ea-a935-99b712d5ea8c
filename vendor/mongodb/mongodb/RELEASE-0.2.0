2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Revert to more traditional error reporting in Makefile

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Add note that docs may require a manual gh-pages push

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Use Makefile error reporting for missing binaries

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Don't clean site/ directory on gh-pages deploy

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Fix trailing semicolons for some Makefile commands

2015-05-12  <PERSON>  <jmi<PERSON><EMAIL>>
	* Consolidate building of API and book docs

2015-05-12  <PERSON>  <<EMAIL>>
	* Exclude merges from generated release log

2015-05-12  <PERSON>  <<EMAIL>>
	* Use dynamic release/VERSION make target

2015-05-12  <PERSON>  <<EMAIL>>
	* Fix Makefile indentation

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Update docs for 0.2

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Update code examples for 0.2

2015-05-12  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* PHPLIB-105: Remove Collection::VERSION constant

2015-05-12  <PERSON> <PERSON>kola  <<EMAIL>>
	* PHPLIB-83: Extend driver exceptions

2015-05-12  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-87: Remove manual bsonSerialize() call

2015-05-05  Jeremy Mikola  <<EMAIL>>
	* Do not iterate on CollectionInfoIterator multiple times

2015-05-05  Jeremy Mikola  <<EMAIL>>
	* Bump ext-mongodb dependency to 0.6.0

2015-05-05  Jeremy Mikola  <<EMAIL>>
	* Remove Faker test dependency

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-94: Functional tests for bulkWrite() and BulkWriteResult

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-103: bulkWrite() updateMany should require update operators

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-94: BulkWriteResult and collect inserted IDs in bulkWrite()

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* InsertManyResult should require $insertedIds array

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* UpdateResult::getUpsertedId() may return any value

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-102: Implement UpdateResult::getUpsertedCount()

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* Revise InsertManyResult::getInsertedIds() documentation

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* Fix iteration on operations in bulkWrite()

2015-05-03  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-92: bulkWrite() updates should use "multi" option

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* Note that UpdateResult::getModifiedCount() is undefined for legacy ops

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* Fix documentation for getInsertedId(s) methods on insert results

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-89: String getters for Database and Collection classes

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* Reduce default server selection timeout for tests

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-98: Ensure mongod service is started for all versions

2015-04-30  Jeremy Mikola  <<EMAIL>>
	* Do not check modifiedCount for updates on 2.4

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* Skip $out aggregation test for MongoDB 2.4

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-86: Fix aggregate() useCursor default and 2.4 compatibility

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-98: Add multiple server versions to Travis CI

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-95: Massage findAndModify null results before 3.0

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-97: Cast count() results to integers

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-96: Fix replacement/upsert test failures for 2.4

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-58: Functional tests for CRUD spec write methods

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* Hash test case names to avoid hitting namespace limits

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* Clean up after passing Collection functional tests

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* Return findAndModify result document as an array

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* Restore Collection::_massageFindAndModifyOptions()

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-93: Insert result classes should always track IDs

2015-04-29  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-92: Update methods should use "multi" option

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-58: Functional tests for CRUD spec read methods

2015-04-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-91: Ensure count/distinct filters serialize as BSON objects

2015-04-27  Jeremy Mikola  <<EMAIL>>
	* Split Database and Collection functional tests

2015-04-27  Jeremy Mikola  <<EMAIL>>
	* Reorder methods in CollectionInfo

2015-04-27  Jeremy Mikola  <<EMAIL>>
	* Pedantic method declaration test should ignore inherited methods

2015-04-27  Jeremy Mikola  <<EMAIL>>
	* Reorder methods for reasons of pedantry

2015-03-18  Jeremy Mikola  <<EMAIL>>
	* Move pedantic method declaration test to its own file

2015-04-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-79: Add __debugInfo() handlers for info classes

2015-04-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-75: Unit tests for database, collection, and index models

2015-04-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-88: Rename IndexInfo::getKeys() to getKey()

2015-04-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-77: Use namespaced exceptions

2015-04-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Fix index creation for legacy servers

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* Bump ext-mongodb dependency to 0.5.1

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* Use wire protocol version constants for feature detection

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Refactor to avoid else condition and void methods

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Test custom name for index creation

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-69: Do not allow empty index name for dropIndex()

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-75: Use package BadMethodCallException for IndexInfo

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Empty input to createIndexes() is a NOP

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Use model class to validate index creation args

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-46, PHPLIB-63, PHPLIB-69: Functional tests for index methods

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Index creation methods

2015-04-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-46: Index enumeration methods

2015-04-22  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-46: Index info and corresponding iterator class

2015-04-22  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-75: Refactor model classes and add class-level docs

2015-04-22  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-70: Add class-level docs to model iterators

2015-04-22  Jeremy Mikola  <<EMAIL>>
	* Use type map for database and collection enumeration

2015-04-22  Jeremy Mikola  <<EMAIL>>
	* Fix word wrap in documentation

2015-04-21  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-69: Index drop methods

2015-04-13  Jeremy Mikola  <<EMAIL>>
	* Link to interface docs from implementations

2015-04-13  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-72: Use model class when listing databases

2015-04-13  Jeremy Mikola  <<EMAIL>>
	* Link to canonical documentation URL

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* Don't be quiet when compiling the extension

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* Install latest PECL extension when testing

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: Test listCollections with filter option

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: Support stdClass listCollections() filter option

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: List collections according to wire protocol version

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: Construct CollectionInfoLegacyIterator from Traversable

2015-04-06  Jeremy Mikola  <<EMAIL>>
	* Allow any extension version for development

2015-04-06  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-64: Collection creation method

2015-04-06  Jeremy Mikola  <<EMAIL>>
	* Handle new Cursor and toArray() API in extension

2015-03-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: Collection enumeration methods

2015-03-27  Jeremy Mikola  <<EMAIL>>
	* Setup Database object in DatabaseFunctionalTest

2015-03-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-71: Collection drop methods

2015-03-26  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-72: Database enumeration method

2015-03-25  Jeremy Mikola  <<EMAIL>>
	* Restructure CollectionTest and fixture generation functions

2015-03-25  Jeremy Mikola  <<EMAIL>>
	* Use 127.0.0.1 instead of localhost for default URI

2015-03-25  Jeremy Mikola  <<EMAIL>>
	* Dump server buildInfo before running tests on Travis CI

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Cast expected string arguments

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-65: Database drop methods

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-73: Inherit default write concern and read preferences

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* Create base classes for unit and functional tests

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* Create test bootstrap for autoloading

2015-04-10  Jeremy Mikola  <<EMAIL>>
	* Link to the HHVM driver alongside PHP

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Remove unsupported options in ApiGen config

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Rename version variable in Makefile

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Fix typos in Makefile

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Fix database name in example docs

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Update documentation for PECL mongodb-0.2.0

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Fix function array dereferencing syntax for PHP 5.3

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-56: Travis CI configuration

2015-03-23  Jeremy Mikola  <<EMAIL>>
	* Update readme and composer.json for PECL mongodb-0.2.0

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-45: Prototype for collection enumeration method

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-46: Prototypes for index enumeration method

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* Declare Collection methods alphabetically by visibility

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-71: Prototypes for collection drop methods

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-65: Prototypes for database drop methods

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-64: Prototype for collection create method

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-63: Prototypes for index creation methods

2015-03-17  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-69: Prototypes for index drop methods

2015-03-16  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-59: Implement insertMany() and InsertManyResult

2015-03-16  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-60: Create result classes for CRUD methods

2015-03-16  Hannes Magnusson  <<EMAIL>>
	* Bump phongo version and link to jira

2015-03-12  Hannes Magnusson  <<EMAIL>>
	* Add MongoClient and Database objects

2015-03-12  Hannes Magnusson  <<EMAIL>>
	* missing argument doc

2015-03-12  Hannes Magnusson  <<EMAIL>>
	* PHPLIB-62: Its called BulkWrite now

2015-02-19  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-54: Update references to driver classes

2015-02-19  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-44: php-cs-fixer fix src/ --level=psr2

2014-12-19  Jeremy Mikola  <<EMAIL>>
	* Rename package to mongodb/mongodb and update metadata

2014-12-19  Jeremy Mikola  <<EMAIL>>
	* Add @jmikola and @derickr to Composer author list

2014-12-19  Jeremy Mikola  <<EMAIL>>
	* Add @jmikola to author list

2014-12-19  Jeremy Mikola  <<EMAIL>>
	* Specify Apache 2 license

2014-12-12  Jeremy Mikola  <<EMAIL>>
	* Reformat composer.json

2014-12-12  Jeremy Mikola  <<EMAIL>>
	* Fix package name in composer.json

2014-12-12  Hannes Magnusson  <<EMAIL>>
	* Update links after moving the repo from bjori to 10gen-labs

2014-12-12  Hannes Magnusson  <<EMAIL>>
	* README updates

2014-12-11  Hannes Magnusson  <<EMAIL>>
	* Improve steps needed

2014-12-11  Hannes Magnusson  <<EMAIL>>
	* Add 0.1.0 release notes
