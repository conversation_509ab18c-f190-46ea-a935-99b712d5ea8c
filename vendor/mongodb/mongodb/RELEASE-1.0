RELEASE 1.0.0-beta1
-------------------
2015-11-02  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Update install docs for PHPC stable and PHPLIB beta

2015-11-01  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Shorthand array syntax

2015-11-01  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Test with mongodb-1.0 on Travis CI

2015-11-01  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* MongoDB\Manager no longer has single write methods

2015-11-01  <PERSON>  <j<PERSON><PERSON><EMAIL>>
	* MongoDB\Driver\BulkWrite now takes an options array

2015-11-01  <PERSON>  <<EMAIL>>
	* Remove helper functions now that Manager RP/WC getters exist

2015-10-23  <PERSON>  <jmi<PERSON><EMAIL>>
	* Update more GitHub URLs

2015-10-23  <PERSON>  <jmi<PERSON>@gmail.com>
	* Update GitHub URLs in docs

2015-10-09  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* Ensure database is dropped before asserting it doesn't exist

2015-10-09  <PERSON>  <j<PERSON><PERSON><PERSON>@gmail.com>
	* PHPLIB-133: Support typeMap option in FindOne

2015-10-09  <PERSON>  <<EMAIL>>
	* Functional test for aggregate command failure

2015-10-09  <PERSON> <PERSON>kola  <<EMAIL>>
	* Remove redundant option validation from FindAndModify children

2015-10-09  Jeremy Mikola  <<EMAIL>>
	* Remove unnecessary use statement

2015-10-09  <PERSON> Mi<PERSON>la  <<EMAIL>>
	* Refactor functional tests to use DropCollection

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Update extension installation docs for PHP and HHVM

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Update installation instructions in docs

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Consolidate release notes by minor version

2015-10-07  Jeremy Mikola  <<EMAIL>>
	* Add 1.0.0-alpha1 release notes

RELEASE 1.0.0-alpha1
--------------------
2015-10-06  Jeremy Mikola  <<EMAIL>>
	* Use "autoload-dev" for loading test classes

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* Replace magic string with a private constant and comments

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* ListCollections functional tests

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-123: Do not throw when listing indexes on nonexistent collection

2015-09-28  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-107: Fix return statement when dropping nonexistent collection

2015-09-27  Jeremy Mikola  <<EMAIL>>
	* DropDatabase functional tests

2015-09-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-107: Do not throw when dropping nonexistent collection

2015-09-24  Jeremy Mikola  <<EMAIL>>
	* Revise docs and exception message for assertDatabaseExists()

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* Trust that Collection's writeConcern is always set

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* Relax writeConcern option checks in operation classes

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-111: Ensure read ops use appropriate read preference

2015-09-14  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-73: Database and Collection can inherit RP/WC from Manager

2015-09-12  Jeremy Mikola  <<EMAIL>>
	* Bump extension version to beta

2015-09-24  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-120: Require PHP 5.4+

2015-09-18  Daniel Kozak  <<EMAIL>>
	* fix doc url

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Database $databaseName and test getters

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Collection $namespace and test getters

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Rename Collection class properties

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Fix type documentation for Count and Distinct $filter arg

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Disable xdebug extension on Travis

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Find operation

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Aggregate operation

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Distinct operation and allow array/object $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Unit tests for Count operation and allow array/object $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Validate Aggregation $pipeline before $options

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Allow array/object for Collection::find() and findOne() $filter

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Test type checking for BulkWrite constructor options

2015-09-03  Jeremy Mikola  <<EMAIL>>
	* Refactor unit tests for write operations

2015-09-02  Jeremy Mikola  <<EMAIL>>
	* Rely on default type map conversion

2015-09-02  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-73: Database and Collection should inherit Manager's WC and RP

2015-08-30  Jeremy Mikola  <<EMAIL>>
	* Print core dumps for segfaults on Travis

2015-08-31  Jeremy Mikola  <<EMAIL>>
	* Remove copypasta in CreateCollection

2015-08-31  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract BulkWrite operation class

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract ReplaceOne, UpdateOne, and UpdateMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract DeleteOne and DeleteMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-109: Extract InsertOne and InsertMany operation classes

2015-08-27  Jeremy Mikola  <<EMAIL>>
	* Refer to findAndModify docs in related Collection methods

2015-07-27  Derick Rethans  <<EMAIL>>
	* PHPC-118: Expect documents as objects (new default type)

2015-07-27  Derick Rethans  <<EMAIL>>
	* PHPLIB-118: Specify "root" option in typemap, as it's separate from "document"

2015-08-26  Jeremy Mikola  <<EMAIL>>
	* Require ext-mongodb ^1.0.0

2015-08-26  Jeremy Mikola  <<EMAIL>>
	* Bump dev-master to 0.3.x-dev

2015-06-18  Jeremy Mikola  <<EMAIL>>
	* PHPLIB-110: Extract Find and FindOne operation classes

2015-06-30  Derick Rethans  <<EMAIL>>
	* PHPLIB-108: Use MongoDB\BSON namespace prefix

2015-06-19  Jeremy Mikola  <<EMAIL>>
	* Make expected document assertions more flexible

2015-06-19  Jeremy Mikola  <<EMAIL>>
	* Ensure operations return documents as objects by default

2015-06-18  Derick Rethans  <<EMAIL>>
	* Because the typemap says 'document as array', we now need to change the return value with a cast to 'object'

2015-06-18  Derick Rethans  <<EMAIL>>
	* Compare all arrays of documents by setting the typemap for documents to 'array'.

2015-06-18  Derick Rethans  <<EMAIL>>
	* Use type map to force arrays instead of objects.

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Aggregate should check server support before returning a cursor

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Relax assertion in AggregateFunctionalTest

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Older servers may return count "n" as a float

2015-06-17  Jeremy Mikola  <<EMAIL>>
	* Don't assume document PHP type mapping in FunctionalTestCase

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Remove unused Collection constants and methods

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* FeatureDetection utility class is obsolete

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* assertCommandSucceeded() now accepts a result document

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract DropIndexes operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Database::createCollection() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extra DropCollection operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract DropDatabase operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::listIndexes() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Database::listCollections() to an operation class

2015-06-16  Jeremy Mikola  <<EMAIL>>
	* Extract Client::listDatabases() to an operation class

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Replace private methods with generate_index_name() function

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Return documents as objects from Collection findAndModify methods

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Extract Collection findAndModify methods to operation classes

2015-06-11  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::count() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::createIndexes() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::distinct() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Extract Collection::aggregate() to an operation class

2015-06-10  Jeremy Mikola  <<EMAIL>>
	* Executable interface for operations

2015-06-14  Jeremy Mikola  <<EMAIL>>
	* Create functions.php file for utility functions

2015-05-06  Jeremy Mikola  <<EMAIL>>
	* Split UnexpectedTypeException for logic and runtime errors

2015-05-14  Sergey  <<EMAIL>>
	* Update data.md

2015-05-12  Jeremy Mikola  <<EMAIL>>
	* Reminder to push current branch (not just tags) for release

2015-05-12  Jeremy Mikola  <<EMAIL>>
	* Add 0.2.0 release notes
