2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* PHP-1326: Makefile for testing/releasing & stuffz

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* Add a version constant

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* Missed committing the helper functions

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* Add test for insertOne(), findOne(), count(), ..

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* Do not merge options, mongod doesn't like empty ones

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* Importing json, finding one document

2014-12-11  <PERSON><PERSON>  <<EMAIL>>
	* PHP-1335: Add Collection::findOne() helper

2014-12-10  <PERSON><PERSON>  <<EMAIL>>
	* PHP-1327: Initial commit of PHongo CRUD docs

2014-12-10  <PERSON><PERSON>  <<EMAIL>>
	* ignore apidoc

2014-12-10  <PERSON><PERSON>  <<EMAIL>>
	* Add experimental apigen config file

2014-12-10  <PERSON><PERSON>  <<EMAIL>>
	* Add example referenced from Collection::bulkWrite()

2014-12-10  <PERSON><PERSON> <PERSON><PERSON>  <<EMAIL>>
	* PHP-1327: Add docblocks

2014-12-10  Hannes Magnusson  <<EMAIL>>
	* Fetch the correct option set

2014-12-10  Jeremy Mikola  <<EMAIL>>
	* Add default PHPUnit configuration

2014-12-10  Hannes Magnusson  <<EMAIL>>
	* PHP-1331: Implement Collection::getDatabaseName() and Collection::getCollectionName()

2014-12-10  Hannes Magnusson  <<EMAIL>>
	* deleteMany is supposed to have no limit

2014-12-10  Hannes Magnusson  <<EMAIL>>
	* PHP-1332: Throw InvalidArgumentException on argument errors

2014-12-10  Hannes Magnusson  <<EMAIL>>
	* Move constants from namespace constants to class constants

2014-12-09  Hannes Magnusson  <<EMAIL>>
	* Lets see if this works for phongo...

2014-12-09  Hannes Magnusson  <<EMAIL>>
	* ws

2014-12-09  Hannes Magnusson  <<EMAIL>>
	* ignore the composer lock

2014-12-09  Hannes Magnusson  <<EMAIL>>
	* Add "PSR-4" autoloading

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* Bump dependency

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* folding markers

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* PHP-1312: Implement Collection::bulkWrite()

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* write.php contains all examples for now

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* Regenerate

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* PHP-1300: Create WriteResult classes

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* PHP-1315: Implement Collection::findOneAndUpdate()

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* PHP-1313: Implement Collection::findOneAndDelete()

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* PHP-1314: Implement Collection::findOneAndReplace()

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* Fix replacing a document after fixing PHP-1320

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* folding

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* improve throw/catch frequency

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* Missed deleting an empty file after refactoring

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* Ignore local ini

2014-12-08  Hannes Magnusson  <<EMAIL>>
	* I have no idea why, but this is apparently not valid syntax in php

2014-12-05  Hannes Magnusson  <<EMAIL>>
	* Improve Command Cursor suport

2014-12-05  Hannes Magnusson  <<EMAIL>>
	* PHP-1301: Collection::aggregate()

2014-12-05  Hannes Magnusson  <<EMAIL>>
	* Fix option parsing and pass correct options to distinct

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1303: Collection::distinct()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1302: Collection::count()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Make this more namespace and autoload friendly, class per file

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1309: Collection::replaceOne()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* CRUD API examples

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1310 & PHP-1311: Collection::update[One|Many]()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1307 & PHP-1308: Collection::delete[One|Many]()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1305: Collection::insertOne()

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Naming consistency with the spec

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Implement write helper

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Reorganize and add folding markers

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Initial commit

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Initial commit

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* Basic ignore

2014-12-04  Hannes Magnusson  <<EMAIL>>
	* PHP-1304: Implement Collection::find()
