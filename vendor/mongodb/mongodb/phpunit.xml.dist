<?xml version="1.0" encoding="UTF-8"?>

<phpunit
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/4.3/phpunit.xsd"
         backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         stopOnFailure="false"
         syntaxCheck="false"
         bootstrap="tests/bootstrap.php"
>

    <php>
        <ini name="error_reporting" value="-1"/>
        <env name="MONGODB_URI" value="mongodb://127.0.0.1:27017/?serverSelectionTimeoutMS=100"/>
        <env name="MONGODB_DATABASE" value="phplib_test"/>
    </php>

    <testsuites>
        <testsuite name="MongoDB CRUD Test Suite">
            <directory>./tests/</directory>
        </testsuite>
    </testsuites>
</phpunit>
