(function(){(function(f,b){"object"===typeof exports&&exports?b(exports):"function"===typeof define&&define.amd?define(["exports"],b):b(f.Mustache={})})(this,function(f){function b(a){return"function"===typeof a}function d(a){return a.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function t(a,e){function c(a){"string"===typeof a&&(a=a.split(C,2));if(!A(a)||2!==a.length)throw Error("Invalid tags: "+a);m=new RegExp(d(a[0])+"\\s*");n=new RegExp("\\s*"+d(a[1]));t=new RegExp("\\s*"+d("}"+a[1]))}if(!a)return[];
var g=[],y=[],b=[],k=!1,h=!1,m,n,t;c(e||f.tags);for(var p=new r(a),v,l,u,w;!p.eos();){v=p.pos;if(u=p.scanUntil(m)){w=0;for(var z=u.length;w<z;++w)if(l=u.charAt(w),D.call(E,l)?h=!0:b.push(y.length),y.push(["text",l,v,v+1]),v+=1,"\n"===l){if(k&&!h)for(;b.length;)delete y[b.pop()];else b=[];h=k=!1}}if(!p.scan(m))break;k=!0;l=p.scan(F)||"name";p.scan(G);"="===l?(u=p.scanUntil(B),p.scan(B),p.scanUntil(n)):"{"===l?(u=p.scanUntil(t),p.scan(H),p.scanUntil(n),l="&"):u=p.scanUntil(n);if(!p.scan(n))throw Error("Unclosed tag at "+
p.pos);w=[l,u,v,p.pos];y.push(w);if("#"===l||"^"===l)g.push(w);else if("/"===l){l=g.pop();if(!l)throw Error('Unopened section "'+u+'" at '+v);if(l[1]!==u)throw Error('Unclosed section "'+l[1]+'" at '+v);}else"name"===l||"{"===l||"&"===l?h=!0:"="===l&&c(u)}if(l=g.pop())throw Error('Unclosed section "'+l[1]+'" at '+p.pos);return q(x(y))}function x(a){for(var e=[],c,g,b=0,d=a.length;b<d;++b)if(c=a[b])"text"===c[0]&&g&&"text"===g[0]?(g[1]+=c[1],g[3]=c[3]):(e.push(c),g=c);return e}function q(a){for(var e=
[],c=e,g=[],b,d=0,k=a.length;d<k;++d)switch(b=a[d],b[0]){case "#":case "^":c.push(b);g.push(b);c=b[4]=[];break;case "/":c=g.pop();c[5]=b[2];c=0<g.length?g[g.length-1][4]:e;break;default:c.push(b)}return e}function r(a){this.tail=this.string=a;this.pos=0}function n(a,e){this.view=a;this.cache={".":this.view};this.parent=e}function m(){this.cache={}}var I=Object.prototype.toString,A=Array.isArray||function(a){return"[object Array]"===I.call(a)},D=RegExp.prototype.test,E=/\S/,J={"&":"&amp;","<":"&lt;",
">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"},G=/\s*/,C=/\s+/,B=/\s*=/,H=/\s*\}/,F=/#|\^|\/|>|\{|&|=|!/;r.prototype.eos=function(){return""===this.tail};r.prototype.scan=function(a){a=this.tail.match(a);if(!a||0!==a.index)return"";a=a[0];this.tail=this.tail.substring(a.length);this.pos+=a.length;return a};r.prototype.scanUntil=function(a){a=this.tail.search(a);var e;switch(a){case -1:e=this.tail;this.tail="";break;case 0:e="";break;default:e=this.tail.substring(0,a),this.tail=this.tail.substring(a)}this.pos+=
e.length;return e};n.prototype.push=function(a){return new n(a,this)};n.prototype.lookup=function(a){var e=this.cache,c;if(a in e)c=e[a];else{for(var g=this,d,f,k=!1;g;){if(0<a.indexOf("."))for(c=g.view,d=a.split("."),f=0;null!=c&&f<d.length;)f===d.length-1&&null!=c&&(k="object"===typeof c&&c.hasOwnProperty(d[f])),c=c[d[f++]];else null!=g.view&&"object"===typeof g.view&&(c=g.view[a],k=g.view.hasOwnProperty(a));if(k)break;g=g.parent}e[a]=c}b(c)&&(c=c.call(this.view));return c};m.prototype.clearCache=
function(){this.cache={}};m.prototype.parse=function(a,e){var c=this.cache,b=c[a];null==b&&(b=c[a]=t(a,e));return b};m.prototype.render=function(a,e,c){var b=this.parse(a);e=e instanceof n?e:new n(e);return this.renderTokens(b,e,c,a)};m.prototype.renderTokens=function(a,e,c,b){for(var d="",f,k,h,m=0,n=a.length;m<n;++m)h=void 0,f=a[m],k=f[0],"#"===k?h=this._renderSection(f,e,c,b):"^"===k?h=this._renderInverted(f,e,c,b):">"===k?h=this._renderPartial(f,e,c,b):"&"===k?h=this._unescapedValue(f,e):"name"===
k?h=this._escapedValue(f,e):"text"===k&&(h=this._rawValue(f)),void 0!==h&&(d+=h);return d};m.prototype._renderSection=function(a,e,c,d){function f(a){return m.render(a,e,c)}var m=this,k="",h=e.lookup(a[1]);if(h){if(A(h))for(var n=0,q=h.length;n<q;++n)k+=this.renderTokens(a[4],e.push(h[n]),c,d);else if("object"===typeof h||"string"===typeof h||"number"===typeof h)k+=this.renderTokens(a[4],e.push(h),c,d);else if(b(h)){if("string"!==typeof d)throw Error("Cannot use higher-order sections without the original template");
h=h.call(e.view,d.slice(a[3],a[5]),f);null!=h&&(k+=h)}else k+=this.renderTokens(a[4],e,c,d);return k}};m.prototype._renderInverted=function(a,e,c,b){var d=e.lookup(a[1]);if(!d||A(d)&&0===d.length)return this.renderTokens(a[4],e,c,b)};m.prototype._renderPartial=function(a,e,c){if(c&&(a=b(c)?c(a[1]):c[a[1]],null!=a))return this.renderTokens(this.parse(a),e,c,a)};m.prototype._unescapedValue=function(a,b){var c=b.lookup(a[1]);if(null!=c)return c};m.prototype._escapedValue=function(a,b){var c=b.lookup(a[1]);
if(null!=c)return f.escape(c)};m.prototype._rawValue=function(a){return a[1]};f.name="mustache.js";f.version="2.0.0";f.tags=["{{","}}"];var z=new m;f.clearCache=function(){return z.clearCache()};f.parse=function(a,b){return z.parse(a,b)};f.render=function(a,b,c){return z.render(a,b,c)};f.to_html=function(a,d,c,g){a=f.render(a,d,c);if(b(g))g(a);else return a};f.escape=function(a){return String(a).replace(/[&<>"'\/]/g,function(a){return J[a]})};f.Scanner=r;f.Context=n;f.Writer=m});(function(){function f(b){return"".trim?
b.trim():b.replace(/^\s+/,"").replace(/\s+$/,"")}var b={VERSION:"0.10.2",templates:{},Mustache:Mustache,$:"undefined"!==typeof window?window.jQuery||window.Zepto||null:null,addTemplate:function(d,t){if("object"===typeof d)for(var x in d)this.addTemplate(x,d[x]);else b[d]?console.error("Invalid name: "+d+"."):b.templates[d]?console.error('Template "'+d+'  " exists'):(b.templates[d]=t,b[d]=function(q,r){q=q||{};var n=Mustache.to_html(b.templates[d],q,b.templates);return b.$&&!r?b.$(f(n)):n})},clearAll:function(){for(var d in b.templates)delete b[d];
b.templates={}},refresh:function(){b.clearAll();b.grabTemplates()},grabTemplates:function(){var d,t,x=document.getElementsByTagName("script"),q,r=[];d=0;for(t=x.length;d<t;d++)(q=x[d])&&q.innerHTML&&q.id&&("text/html"===q.type||"text/x-icanhaz"===q.type)&&(b.addTemplate(q.id,f(q.innerHTML)),r.unshift(q));d=0;for(t=r.length;d<t;d++)r[d].parentNode.removeChild(r[d])}};"undefined"!==typeof exports?("undefined"!==typeof module&&module.exports&&(exports=module.exports=b),exports.ich=b):this.ich=b;"undefined"!==
typeof document&&(b.$?b.$(function(){b.grabTemplates()}):document.addEventListener("DOMContentLoaded",function(){b.grabTemplates()},!0))})()})();
