<?php
if( php_sapi_name() !== 'cli' ) {
    die("Meant to be run from command line");
}

function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        //it is possible to check for other files here
        if( file_exists($dir."/wp-config.php") ) {
            return $dir;
        }
    } while( $dir = realpath("$dir/..") );
    return null;
}

// define( 'BASE_PATH', find_wordpress_base_path()."/" );
define('BASE_PATH', '/Users/<USER>/server/pindiline/');
define('WP_USE_THEMES', false);
global $wp, $wp_query, $wp_the_query, $wp_rewrite, $wp_did_header;
require(BASE_PATH . 'wp-load.php');

class NnaLimitedAPI extends NnaApi {
	private static $instance;
	public $limit = 1000;
	public $offset = 0;

	public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getObjectsWsdl()
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['user']) || !isset($options['password'])) {
            throw new Exception('No username or password');
        }
        ini_set("soap.wsdl_cache_enabled", "0");
        $context = stream_context_create(array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ));
        return new RealEstateDataExchange(
            'https://versions.next.ee/_next_development/maintenance/RealEstate/REDE.wsdl',
            array(
                'login' => $options['user'],
                'password' => $options['password'],
                'trace' => 1,
                'stream_context' => $context,
            )
        );
    }

    public function getWsdlParams($request)
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['user']) || !isset($options['password'])) {
            throw new Exception('No username or password');
        }
        $params = new $request();
        $params->username = $options['user'];
        $params->password = $options['password'];
        return $params;
    }

    public function updateObjects($ids, $client)
    {
        $existing = array();
        $collection = $this->getCollection($this->getDbName().'.objects');
        $sobject=array();
        $sobject['Object.originalId']['$in']=$ids;
        $counter = 0;
        foreach ($collection->find($sobject, array('noCursorTimeout' => true)) as $object) {
            echo '.';
            $id = $object->Object->originalId;
            echo $id;
            $existing[] = $id;
        	if ($counter >= $this->offset && $counter < ($this->limit + $this->offset)) {
                echo 'checking ';
	            $params = $this->getWsdlParams('getObjectById');
	            $params->originalId = $id;
	            try {
	                $rs = $client->getObjectById($params);
	            } catch (SoapFault $e) {
	                $client = $this->getObjectsWsdl();
	                $rs = $client->getObjectById($params);
	            }

	            file_put_contents(NNA_PATH . '/log/synclog.log', 'checking: #'.$rs->Object->originalId.PHP_EOL, FILE_APPEND);

	            $ours=new DateTime($object->Object->lastModified);
	            $their=new DateTime($rs->Object->lastModified);
	            if($their>$ours){
                    echo 'updating ';
	                $this->updateObject($rs, $object, $collection);
	                $params = $this->getWsdlParams('objectUpdateCompleted');
	                $params->Objects = $rs;
	                $response = $client->objectUpdateCompleted($params);
	            }
        	}

            echo PHP_EOL;
            $counter++;
        }
        return array_diff($ids, $existing);
    }

    public function syncAll()
    {
        ini_set("memory_limit", "1024M");
        echo 'Starting sync all with limit: ' . $this->limit . ' offset: ' . $this->offset . PHP_EOL;

        $response = array();
        $response['updated'] = 0;
        $response['inserted'] = 0;

        try {
            $client = $this->getObjectsWsdl();
            $params = $this->getWsdlParams('getObjectIdsByStatus');
            $params->status = 'active';

            $ids = $client->getObjectIdsByStatus($params)->originalId;
            echo 'Total objects to process: ' . count($ids) . PHP_EOL;

            // Process objects in chunks based on limit and offset
            $chunkedIds = array_slice($ids, $this->offset, $this->limit);
            echo 'Processing chunk: ' . count($chunkedIds) . ' objects' . PHP_EOL;

            $updated = $this->updateObjects($chunkedIds, $client);
            $response['updated'] = count($chunkedIds) - count($updated);
            $response['inserted'] = 0; // This implementation focuses on updates

            echo 'Sync completed successfully' . PHP_EOL;

        } catch (Exception $e) {
            echo 'Error during sync: ' . $e->getMessage() . PHP_EOL;
            file_put_contents(NNA_PATH . '/log/synclog.log', 'Error: ' . $e->getMessage() . PHP_EOL, FILE_APPEND);
        }

        return $response;
    }
}

$instance = NnaLimitedAPI::getInstance();
$instance->limit = 1000;
$instance->offset = 0;
$instance->startObjectSync(true);

$response = NnaLimitedAPI::getInstance()->syncAll();
echo 'Total updated: ' . $response['updated'] . ' inserted: ' . $response['inserted'] . "\n";