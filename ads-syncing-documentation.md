# Ads Syncing Documentation

## Overview
This document describes the ads syncing functionality in the Newtime Next API plugin, which synchronizes real estate advertisements from external APIs (Brenollis and Scoro) into the WordPress/MongoDB system.

## Admin Interface Location
- **URL**: `https://pindiline.site.test/wp-admin/admin.php?page=newtime-next-api`
- **Page Title**: "Newtime Next API"
- **Description**: Main admin page with sync buttons

## Sync Buttons Available

### 1. "Sünkroniseeri objektid" (Sync Objects from Scoro)
- **Button ID**: `#nna-sync-objects`
- **AJAX Action**: `nnaSyncObjects`
- **Handler**: `ScoroSync::getInstance()->startObjectSync(false)`

### 2. "Sünkroniseeri kasutajad" (Sync Users)
- **Button ID**: `#nna-sync-users`
- **AJAX Action**: `nnaSyncUsers`
- **Handler**: `ScoroSync::getInstance()->syncUsers()`

### 3. "Sünkroniseeri objektid brenollisest" (Sync Objects from Brenollis)
- **Button ID**: `#nna-sync-brenollis-objects`
- **AJAX Action**: `nnaSyncBrenollisObjects`
- **Handler**: `BrenollisSync::getInstance()->startObjectSync()`

## File Structure and Flow

### 1. Main Admin Page
**File**: `plugin.php`
**Location**: Lines 78-90
**Function**: `viewIndex()`

```php
public function viewIndex()
{
    echo '<h1>Tere! Muuda settingute alt seaded sobivaks.</h1>';
    echo '<div>';
        echo '<button class="button button-primary" id="nna-sync-objects">Sünkroniseeri objektid</button>';
    echo '</div>';
    echo '<div>';
        echo '<button class="button button-primary" id="nna-sync-users">Sünkroniseeri kasutajad</button>';
    echo '</div>';
    echo '<div>';
        echo '<button class="button button-primary" id="nna-sync-brenollis-objects">Sünkroniseeri objektid brenollisest</button>';
    echo '</div>';
}
```

### 2. JavaScript Handler
**File**: `assets/js/admin.js`
**Location**: Lines 1-44

Handles button clicks and sends AJAX requests:

```javascript
// Sync Objects from Scoro
$('body').on('click', '#nna-sync-objects', function() {
    $.ajax({
        url: ajaxurl,
        type: 'post',
        dataType: 'json',
        data: { action: 'nnaSyncObjects' },
        success: function(data) { console.log(data); }
    });
});

// Sync Objects from Brenollis
$('body').on('click', '#nna-sync-brenollis-objects', function() {
    $.ajax({
        url: ajaxurl,
        type: 'post',
        dataType: 'json',
        data: { action: 'nnaSyncBrenollisObjects' },
        success: function(data) { console.log(data); }
    });
});

// Sync Users
$('body').on('click', '#nna-sync-users', function() {
    $.ajax({
        url: ajaxurl,
        type: 'post',
        dataType: 'json',
        data: { action: 'nnaSyncUsers' },
        success: function(data) { console.log(data); }
    });
});
```

### 3. AJAX Handler
**File**: `lib/Ajax.class.php`
**Location**: Lines 85-101

Processes AJAX requests and routes to appropriate sync classes:

```php
public function syncUsers()
{
    ScoroSync::getInstance()->syncUsers();
}

public function syncObjects()
{
    // NnaApi::getInstance()->startObjectSync(false);
    ScoroSync::getInstance()->startObjectSync(false);
    wp_die();
}

public function syncBrenollisObjects()
{
    BrenollisSync::getInstance()->startObjectSync();
    wp_die();
}
```

## Sync Implementation Classes

### 1. BrenollisSync Class
**File**: `lib/BrenollisSync.class.php`

#### Key Methods:

**`startObjectSync($all = false)`** - Line 479
- Main entry point for Brenollis sync
- If `$all = true`, calls `syncAll()`
- Otherwise performs incremental sync

**`syncAll()`** - Line 316
- Performs full synchronization
- Sets memory limit to 512M
- Processes all active objects in pages

**`getActiveIds()`** - Line 166
- Retrieves all active advertisement IDs from Brenollis API
- Filters by status = 'active'

**`updatePage($page, $size, $daysDiff)`** - Line 394
- Syncs objects page by page
- Default page size: 150 objects
- Handles both updates and inserts

### 2. ScoroSync Class
**File**: `lib/ScoroSync.class.php`

#### Key Methods:

**`startObjectSync($all = false)`** - Line 115
- Main entry point for Scoro sync
- Connects to Next.ee SOAP service
- Handles object synchronization

**`syncUsers()`** - Line 52
- Synchronizes broker/user data
- Updates existing brokers and inserts new ones

**`getObjectsWsdl()`** - Line 15
- Creates SOAP client connection
- Uses credentials from WordPress settings

## Configuration Requirements

### Connection Settings
Required in WordPress admin settings (`nna_connection_settings`):

1. **Brenollis API Settings**:
   - `apiKey` - Brenollis API key
   - `apiUrl` - Brenollis API URL

2. **Next.ee SOAP Settings**:
   - `user` - Next.ee username
   - `password` - Next.ee password
   - `specialUser` - Username for hidden objects
   - `specialPassword` - Password for hidden objects

### MongoDB Settings
Required in WordPress admin settings (`nna_mongo_settings`):

1. **Database Connection**:
   - `dbUrl` - MongoDB connection URL
   - `dbName` - Database name
   - `user` - MongoDB username
   - `password` - MongoDB password

## Logging

### Log Files:
- **Sync Log**: `wp-content/plugins/newtime-next-api/log/synclog.log`
- **Cron Log**: `wp-content/plugins/newtime-next-api/log/cronlog.log`
- **WordPress Debug**: `wp-content/debug.log`

### Log Entries:
- Sync start/end timestamps
- Object update confirmations
- Error messages and exceptions
- Performance metrics

## Command Line Scripts

### syncall.php
**Location**: `wp-content/plugins/newtime-next-api/syncall.php`
- CLI script for batch synchronization
- Uses `NnaLimitedAPI` class with configurable limits
- Can be run via: `php syncall.php`

### cron.php
**Location**: `wp-content/plugins/newtime-next-api/cron.php`
- Automated sync script for cron jobs
- Runs Brenollis sync and cleanup tasks

## Troubleshooting

### Common Issues:

1. **SOAP Connection Errors**:
   - Check Next.ee credentials in settings
   - Verify WSDL URL accessibility
   - Check SSL/TLS configuration

2. **MongoDB Connection Issues**:
   - Verify MongoDB credentials
   - Check database URL and name
   - Ensure MongoDB service is running

3. **Memory Limits**:
   - Scripts set memory_limit to 512M-1024M
   - Adjust if needed for large datasets

4. **API Rate Limits**:
   - Brenollis API may have rate limiting
   - Consider adjusting page sizes and delays

## Related Files

- `loader.php` - Loads all sync classes
- `lib/Settings.class.php` - Admin settings interface
- `lib/WpHandler.class.php` - WordPress post management
- `lib/API.class.php` - Base API class
- `lib/Sync.class.php` - Abstract sync base class
