{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "hash": "d5b075f201805bd13cdf787a5b8c3ca3", "content-hash": "53e1085768ef80cab18693cdf50a645c", "packages": [{"name": "mongodb/mongodb", "version": "1.0.0-beta1", "source": {"type": "git", "url": "https://github.com/mongodb/mongo-php-library.git", "reference": "34b74f852af43fc70f49308bfbd3555f91db593c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mongodb/mongo-php-library/zipball/34b74f852af43fc70f49308bfbd3555f91db593c", "reference": "34b74f852af43fc70f49308bfbd3555f91db593c", "shasum": ""}, "require": {"ext-mongodb": "^1.0.0", "php": ">=5.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3.x-dev"}}, "autoload": {"psr-4": {"MongoDB\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "MongoDB driver library", "homepage": "https://jira.mongodb.org/browse/PHPLIB", "keywords": ["database", "driver", "mongodb", "persistence"], "time": "2015-11-02 21:31:19"}, {"name": "phpmailer/phpmailer", "version": "v5.2.23", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "7115df4a6f76281109ebe352900c42403b728bb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/7115df4a6f76281109ebe352900c42403b728bb4", "reference": "7115df4a6f76281109ebe352900c42403b728bb4", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"doctrine/annotations": "1.2.*", "jms/serializer": "0.16.*", "phpdocumentor/phpdocumentor": "2.*", "phpunit/phpunit": "4.8.*", "symfony/debug": "2.8.*", "symfony/filesystem": "2.8.*", "symfony/translation": "2.8.*", "symfony/yaml": "2.8.*", "zendframework/zend-cache": "2.5.1", "zendframework/zend-config": "2.5.1", "zendframework/zend-eventmanager": "2.5.1", "zendframework/zend-filter": "2.5.1", "zendframework/zend-i18n": "2.5.1", "zendframework/zend-json": "2.5.1", "zendframework/zend-math": "2.5.1", "zendframework/zend-serializer": "2.5.*", "zendframework/zend-servicemanager": "2.5.*", "zendframework/zend-stdlib": "2.5.1"}, "suggest": {"league/oauth2-google": "Needed for Google XOAUTH2 authentication"}, "type": "library", "autoload": {"classmap": ["class.phpmailer.php", "class.phpmaileroauth.php", "class.phpmaileroauthgoogle.php", "class.smtp.php", "class.pop3.php", "extras/EasyPeasyICS.php", "extras/ntlm_sasl_client.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "time": "2017-03-15 19:32:56"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"mongodb/mongodb": 10}, "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": []}