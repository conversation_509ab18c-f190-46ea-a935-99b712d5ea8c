<!DOCTYPE html>
<!--[if lt IE 9]>      <html class="no-js lt-ie10 lt-ie9" lang="et"><![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10" lang="et"><![endif]-->
<!--[if gt IE 9]><!--> <html class="no-js" lang="et"><!--<![endif]-->
<head>
	<title><?php echo wp_title( '', true, 'left' ); ?></title>


	<?php get_template_part('blocks/b_head'); ?>
</head>

<body class="page-offer">

	<?php get_template_part('blocks/b_header'); ?>

	<div class="container">
		<?php

			$favourites = isset($_SESSION['favourites']) ? $_SESSION['favourites'] : array();
			$queryData = array(
				'transactions.transaction.price._' => array(
					'min' => '',
					'max' => '',
				),
				'transactions.transaction.pricem2._' => array(
					'min' => '',
					'max' => '',
				),
				'floorNumber' => array(
					'min' => '',
					'max' => '',
				),
				'numberOfRooms' => array(
					'min' => '',
					'max' => '',
				),
				'areaSize._' => array(
					'min' => '',
					'max' => '',
				),
				'level1' => '',
				'level2' => '',
				// 'level3' => '',
				'objectTypes.type' => '',
				'transactions.transaction.type' => '',
				'alacrity' => '',
				'keyWord' => '',
			);

			$pricem2 = false;
			if (isset($_GET['filters'])) {
				$queryData = array_merge($queryData, $_GET['filters']);
				if (isset($_GET['filters']['transactions.transaction.pricem2._'])) {
					$pricem2 = true;
				}
			}
			// print_r($queryData);

			$pipeline = array(
				array(
					'$group' => array(
						'_id' => array(
							'level1' => '$Object.level1',
							'level2' => '$Object.level2',
							// 'level3' => '$Object.level3',
						),
					)
				),
				array(
					'$group' => array(
						'_id' => array(
							'level1' => '$_id.level1',
							'level2' => '$_id.level2',
						),
						// 'level3' => array(
						// 	'$push' => '$_id.level3',
						// )
					),

				),
				array(
					'$group' => array(
						'_id' => '$_id.level1',
						'level2' => array(
							'$push' => array(
								'_id' => '$_id.level2',
								// 'level3' => '$level3',
							)
						)
					),
				)
			);

			$hierarchicalSelectors = NnaApi::getInstance()->aggregateObjects($pipeline);
			$levels = array(
				'level1' => array(
					'label' => pll__('County'),
					'allPlaceholder' => pll__('All (County)'),
					'items' => array(),
					'parent' => '',
					'hidden' => false,
				),
				'level2' => array(
					'label' => pll__('City'),
					'allPlaceholder' => pll__('All (City)'),
					'items' => array(),
					'parent' => 'level1',
					'hidden' => !empty($queryData['level1']),
				),
				// 'level3' => array(
				// 	'label' => pll__('Village'),
				//	'allPlaceholder' => pll__('All (Village)'),
				// 	'items' => array(),
				// 	'parent' => 'level2',
				// 	'hidden' => !empty($queryData['level2']),
				// ),
			);
			$selected = array(
				'level1' => false,
				'level2' => false,
			);
			foreach ($hierarchicalSelectors as $level1) {
				$level1Hidden = false;
				$levels['level1']['items'][] = array(
					'name' => $level1->_id,
                    'value' => $level1->_id,
					'parent' => '',
					'hidden' => $level1Hidden,
				);
				$selected['level1'] = $level1->_id === $queryData['level1'];
				if (isset($level1->level2)) {
					foreach ($level1->level2 as $level2) {
						$level2Hidden = $levels['level2']['hidden'] && !$selected['level1'];
						$levels['level2']['items'][] = array(
							'name' => $level2->_id,
                        	'value' => $level2->_id,
							'parent' => $level1->_id,
							'hidden' => $level2Hidden,
						);
						$selected['level2'] = $level2->_id === $queryData['level2'];
						if (isset($level2->level3)) {
							foreach ($level2->level3 as $level3) {
								if (!empty($level3)) {
									$levels['level3']['items'][] = array(
										'name' => $level3,
                        				'value' => $level3,
										'parent' => $level2->_id,
										'hidden' => ($levels['level3']['hidden'] || $level2Hidden) && !$selected['level2']
									);
								}
							}
						}
					}
				}
			}
			$selectors = array();

			$pipeline = array(
				array(
					'$group' => array(
						'_id' => '$Object.objectTypes.type',
					),
				),
			);
			$selectors['objectTypes.type'] = array(
				'label' => pll__('Object type'),
				'allPlaceholder' => pll__('All (Object type)'),
				'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
			);

			$pipeline = array(
				array(
					'$group' => array(
						'_id' => '$Object.transactions.transaction.type',
					),
				),
			);
			$selectors['transactions.transaction.type'] = array(
				'label' => pll__('Transaction type'),
				'allPlaceholder' => pll__('All (Transaction type)'),
				'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
			);

			$pipeline = array(
				array(
					'$group' => array(
						'_id' => '$Object.alacrity',
					),
				),
			);
			$alacrity = array(
				'label' => pll__('Condition'),
				'allPlaceholder' => pll__('All (Condition)'),
				'data' => NnaApi::getInstance()->aggregateForSelector($pipeline),
			);


	        if (isset($_GET['sort'])) {
	            $sort = $_GET['sort'];
	        } else {
				$sort = array('lastModified' => -1);
	        }
	        $sortKey = array_keys($sort)[0];


	        if (isset($_GET['filterOpen'])) {
	            $filterOpen = $_GET['filterOpen'];
	        } else {
				$filterOpen = 0;
	        }

			if (isset($_GET['curPage'])) {
	            $page = (int)$_GET['curPage'];
	        } else{
	        	$page = 1;
	        }
		?>
		<form action="<?php echo get_the_permalink(); ?>" class="nna-filters hidden-xs filter main-filter <?php echo (!$filterOpen ? 'not-detail' : ''); ?>">
			<input name="curPage" value="<?php echo $page ?>" hidden>
			<input name="perPage" value="9" hidden>
			<input class="nna-sort-field" name="sort[<?php echo $sortKey; ?>]" value="<?php echo $sort[$sortKey]; ?>" hidden>
			<input class="nna-has-favourites" name="favourites" value="0" hidden>
			<input class="nna-filter-open" name="filterOpen" value="<?php echo $filterOpen; ?>" hidden>
			<div style="display: none;">
				<?php
				// ids: 502905, 502906, 502907
				// $id = 502907;
				// $post = get_post($id);
				// print_r($post);
				// print_r(get_permalink($post));
				// print_r(pll_get_post_language($id));
				?>
			</div>
			<table class="table filter-table filter-table-top">
				<tr>
					<?php
					$translator = NnaTranslations::getInstance();
					foreach ($selectors as $field => $item) {
						$options = $item['data'];
						usort($options, function($a, $b){
							return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
						});
						$scoroTopItemnames = array(
							'apartment',
						    'house',
						    'cottage',
						);
						$topItemsNames = array();
						$map = NnaApi::getInstance()->getBrenollisValueMap(true);
						foreach ($scoroTopItemnames as $name) {
							if (isset($map[$name])) {
								$topItemsNames[] = $map[$name];
							} else {
								$topItemsNames[] = $name;
							}
						}
						$topItemsNames = array_reverse($topItemsNames);
						$itemsCount = 0;
						foreach ($topItemsNames as $singleName) {
						    foreach ($options as $key => $option) {
						        if ($option->_id == $singleName) {
						            unset($options[$key]);
						            array_unshift($options, $option);
						            $itemsCount++;
						            break;
						        }
						    }
						}
						?>
						<td>
							<div class="nna-dropdown dropdown-filter dropdown">
								<?php
								$selectedValue = '';
								foreach ($options as $option) {
									if ($option->_id == $queryData[$field]) {
										$selectedValue = $option->_id;
									}
								}
								?>
								<input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
								<label class="filter-label"><?php echo $item['label'] ?></label>
								<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle <?php echo (!isset($selectedValue) || empty($selectedValue) ? 'gray' : ''); ?>" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
									<?php
									if ($selectedValue) {
										echo $translator->__($selectedValue);
									} else{
										echo $item['allPlaceholder'];
									}
									?>
									<i class="dropdown-filter-btn-icon"></i>
								</button>
								<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
									<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $item['allPlaceholder']; ?></li>
									<?php foreach ($options as $key => $option) { ?>
										<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
										<?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
										    <li class="dropdown-seperator"></li>
										<?php endif ?>
									<?php } ?>

								</ul>
							</div>
						</td>
					<?php } ?>

					<?php
					foreach ($levels as $field => $options) {
						usort($options['items'], function($a, $b){
						    return strcmp($a['name'], $b['name']);
						});
						$selectedValue = '';
						foreach ($options['items'] as $option) {
							if ($option['name'] == $queryData[$field]) {
								$selectedValue = $option['name'];
							}
						}

						$topItemsNames = array(
                            array(
                                'name' => 'Tallinn',
                                'county' => 'Harju maakond',
                                'target' => 'Tallinn',
                            ),
                            array(
                                'name' => 'Tartu',
                                'county' => 'Tartu maakond',
                                'target' => 'Tartu linn',
                            ),
                            array(
                                'name' => 'Pärnu',
                                'county' => 'Pärnu maakond',
                                'target' => 'Pärnu linn',
                            ),
                        );
                        $topItemsNames = array_reverse($topItemsNames);
                        $itemsCount = 0;
                        foreach ($topItemsNames as $city => $singleName) {
                            foreach ($options['items'] as $key => $option) {
                                if ($option['name'] == $singleName['county']) {
                                    $option['name'] = $singleName['name'];
                                    $option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
                                    array_unshift($options['items'], $option);
                                    $itemsCount++;
                                    break;
                                }
                            }
                        }
					?>
					<td>
						<div class="nna-dropdown dropdown-filter dropdown">
							<input class="nna-hierarchical-selector" value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" data-parent="filters[<?php echo $options['parent'] ?>]" hidden>
							<label class="filter-label"><?php echo $options['label'] ?></label>
							<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<?php
								if ($selectedValue) {
									echo NnaTranslations::getInstance()->__($selectedValue);
								} else{
									echo $options['allPlaceholder'];
								}
								?>
								<i class="dropdown-filter-btn-icon"></i>
							</button>
							<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
								<li class="all nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
								<?php foreach ($options['items'] as $key => $option) { ?>
                                    <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
                                    <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                        <li class="dropdown-seperator"></li>
                                    <?php endif ?>
                                <?php } ?>
							</ul>
						</div>
					</td>
					<?php } ?>

					<td class="js-filter-td">
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<label class="filter-label filter-label-radio">
								<input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.price._]"<?php if (!$pricem2) { echo ' checked';} ?>>
								<span class="filter-radio-btn"></span>
								<?php pll_e('Price'); ?>
							</label>
							<?php
							if ($pricem2) { ?>
								<input class="nna-change-pricetype-field-min filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.pricem2._][min]" value="<?php echo $queryData['transactions.transaction.pricem2._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
								<?php
							} else {
								?>
								<input class="nna-change-pricetype-field-min filter-table-input  filter-table-input-full" type="text" name="filters[transactions.transaction.price._][min]" value="<?php echo $queryData['transactions.transaction.price._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
								<?php
							} ?>
						</div>
						<span class="filter-divider">-</span>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<label class="filter-label filter-label-radio">
								<input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.pricem2._]"<?php if ($pricem2) { echo ' checked';} ?>>
								<span class="filter-radio-btn"></span>
								<?php pll_e('Price per square metre (filter)'); ?>

							</label>
							<?php
							if ($pricem2) { ?>
								<input class="nna-change-pricetype-field-max filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.pricem2._][max]" value="<?php echo $queryData['transactions.transaction.pricem2._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
								<?php
							} else {
								?>
								<input class="nna-change-pricetype-field-max filter-table-input filter-table-input-full" type="text" name="filters[transactions.transaction.price._][max]" value="<?php echo $queryData['transactions.transaction.price._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
								<?php
							}
							?>
						</div>
					</td>
					<td>
						<label class="filter-label js-filter-label"><?php pll_e('Number of rooms'); ?></label>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" type="text" name="filters[numberOfRooms][min]" value="<?php echo $queryData['numberOfRooms']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
						</div>
						<span class="filter-divider">-</span>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" type="text" name="filters[numberOfRooms][max]" value="<?php echo $queryData['numberOfRooms']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
						</div>
					</td>
				</tr>
			</table>
			<table class="table filter-table filter-table-bottom">
				<tr>
					<td class="filter-table-data">
						<div class="js-filter-table-data" style="width:100%;">
							<label class="filter-label filter-label-inline js-keywords-label"><?php pll_e('Keyword'); ?></label>
							<input class="filter-table-input filter-table-input-lg js-keywords-input" name="filters[keyWord]" value="<?php echo $queryData['keyWord']; ?>" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
						</div>
					</td>
					<td class="text-right">
						<button type="button" class="nna-detailed-search-button filter-submit-btn filter-submit-btn-sm js-filter-button"><?php pll_e('Detailed search'); ?></button>
						<button type="submit" class="filter-submit-btn js-filter-button"><?php pll_e('Find objects'); ?></button>
					</td>
				</tr>
			</table>
			<table class="table filter-table  filter-table-hidden">
				<tr class="nna-detailed-search">
					<?php
						$options = $alacrity['data'];
						usort($options, function($a, $b){
                            return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                        });
						$field = 'alacrity';
						$selectedValue = '';
						foreach ($options as $option) {
							if ($option->_id == $queryData[$field]) {
								$selectedValue = $option->_id;
							}
						}
					?>

					<td class="filter-table-data">
						<label class="filter-label  js-filter-label"><?php pll_e('Size'); ?></label>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" name="filters[areaSize._][min]" value="<?php echo $queryData['areaSize._']['min']; ?>" placeholder="<?php pll_e('Size from'); ?>">
						</div>
						<span class="filter-divider">-</span>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" name="filters[areaSize._][max]" value="<?php echo $queryData['areaSize._']['max']; ?>" placeholder="<?php pll_e('Size to'); ?>">
						</div>
					</td>
					<td>
						<div class="nna-dropdown dropdown-filter dropdown js-filter-dropdown">
							<input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
							<label class="filter-label"><?php echo $alacrity['label'] ?></label>
							<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-filter-btn-inline dropdown-toggle dropdown" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
								<?php
								if ($selectedValue) {
									echo NnaTranslations::getInstance()->__($selectedValue);
								} else{
									echo $alacrity['allPlaceholder'];
								}
								?>
								<i class="dropdown-filter-btn-icon"></i>
							</button>
							<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
								<li class="nna-filter-option filter-dropdown-option" value="" text=""><?php echo $alacrity['allPlaceholder']; ?></li>
								<?php foreach ($options as $option) { ?>
									<li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
								<?php } ?>
							</ul>
						</div>
					</td>

					<td class="filter-table-data">
						<label class="filter-label filter-label js-filter-label"><?php pll_e('Floor (filter)'); ?>:</label>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" type="text" name="filters[floorNumber][min]" value="<?php echo $queryData['floorNumber']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
						</div>
						<span class="filter-divider">-</span>
						<div class="filter-table-group filter-table-group-half js-filter-table-group">
							<input class="filter-table-input filter-table-input-full" type="text" name="filters[floorNumber][max]" value="<?php echo $queryData['floorNumber']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
						</div>
					</td>
					<td class="filter-empty">
						<div class="filter-empty-element">
							&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;
							&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;&amp; &amp; &amp; &amp; &amp; &amp; &amp; &amp;
						</div>
					</td>
				</tr>
			</table>
		</form>

		<div class="mob-filter visible-xs">
		    <button class="mob-filter-btn js-mob-filter-btn">
		        <span class="mob-filter-btn-txt">
		             <?php pll_e('Search properties (filter)'); ?>
		        </span>
		    </button>
		    <form class="nna-filters filter mob-filter-dropdown js-mob-filter-dropdown main-filter <?php echo (!$filterOpen ? 'not-detail' : ''); ?>">
		        <input name="curPage" value="$page" hidden>
		        <input name="perPage" value="9" hidden>
		        <input class="nna-sort-field" name="sort[<?php echo $sortKey; ?>]" value="<?php echo $sort[$sortKey]; ?>" hidden>
		        <input class="nna-has-favourites" name="favourites" value="0" hidden>
		        <input class="nna-filter-open" name="filterOpen" value="<?php echo $filterOpen; ?>" hidden>
		        <?php
		        $translator = NnaTranslations::getInstance();
		        foreach ($selectors as $field => $item) {
		            $options = $item['data'];
		            usort($options, function($a, $b){
		                return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
		            });

		            $topItemsNames = array(
                        'apartment',
                        'house',
                        'cottage',
                    );
                    $topItemsNames = array_reverse($topItemsNames);
                    $itemsCount = 0;
                    foreach ($topItemsNames as $singleName) {
                        foreach ($options as $key => $option) {
                            if ($option->_id == $singleName) {
                                unset($options[$key]);
                                array_unshift($options, $option);
                                $itemsCount++;
                                break;
                            }
                        }
                    }
		            ?>
		                <div class="nna-dropdown dropdown-filter dropdown">
		                    <?php
		                    $selectedValue = '';
		                    foreach ($options as $option) {
		                        if ($option->_id == $queryData[$field]) {
		                            $selectedValue = $option->_id;
		                        }
		                    }
		                    ?>
		                    <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
		                    <label class="filter-label"><?php echo $item['label'] ?></label>
		                    <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle <?php echo (!isset($selectedValue) || empty($selectedValue) ? 'gray' : ''); ?>" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
		                        <?php
		                        if ($selectedValue) {
		                            echo NnaTranslations::getInstance()->__($selectedValue);
		                        } else{
		                            pll_e('All');
		                        }
		                        ?>
		                        <i class="dropdown-filter-btn-icon"></i>
		                    </button>
		                    <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
		                        <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php pll_e('All'); ?></li>
		                        <?php foreach ($options as $key => $option) { ?>
		                            <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
		                            <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                        <li class="dropdown-seperator"></li>
                                    <?php endif ?>
		                        <?php } ?>
		                    </ul>
		                </div>
		        <?php } ?>

		        <?php
		        foreach ($levels as $field => $options) {
                    usort($options['items'], function($a, $b){
                        return strcmp($a['name'], $b['name']);
                    });
		            $selectedValue = '';
		            foreach ($options['items'] as $option) {
		                if ($option['name'] == $queryData[$field]) {
		                    $selectedValue = $option['name'];
		                }
		            }

                    $topItemsNames = array(
                        array(
                            'name' => 'Tallinn',
                            'county' => 'Harju maakond',
                            'target' => 'Tallinn',
                        ),
                        array(
                            'name' => 'Tartu',
                            'county' => 'Tartu maakond',
                            'target' => 'Tartu linn',
                        ),
                        array(
                            'name' => 'Pärnu',
                            'county' => 'Pärnu maakond',
                            'target' => 'Pärnu linn',
                        ),
                    );
                    $topItemsNames = array_reverse($topItemsNames);
                    $itemsCount = 0;
                    foreach ($topItemsNames as $city => $singleName) {
                        foreach ($options['items'] as $key => $option) {
                            if ($option['name'] == $singleName['county']) {
                                $option['name'] = $singleName['name'];
                                $option['filter-child-action'] = 'filter-pick-child="' . $singleName['target'] . '"';
                                array_unshift($options['items'], $option);
                                $itemsCount++;
                                break;
                            }
                        }
                    }
		        ?>
				<div class="nna-dropdown dropdown-filter dropdown">
					<input class="nna-hierarchical-selector" value="<?php echo $selectedValue ?>" name="filters[<?php echo $field; ?>]" data-parent="filters[<?php echo $options['parent'] ?>]" hidden>
					<label class="filter-label"><?php echo $options['label'] ?></label>
					<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<?php
						if ($selectedValue) {
							echo NnaTranslations::getInstance()->__($selectedValue);
						} else{
							echo $options['allPlaceholder'];
						}
						?>
						<i class="dropdown-filter-btn-icon"></i>
					</button>
					<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
						<li class="all nna-filter-option filter-dropdown-option" value="" text=""><?php echo $options['allPlaceholder']; ?></li>
						<?php foreach ($options['items'] as $key => $option) { ?>
                            <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option['value'] ?>" data-parent="<?php echo $option['parent'] ?>" <?php echo (isset($option['filter-child-action']) ? $option['filter-child-action'] : ''); ?>><?php echo $option['name']; ?></li>
                            <?php if ($itemsCount != 0 && $key == $itemsCount - 1): ?>
                                <li class="dropdown-seperator"></li>
                            <?php endif ?>
                        <?php } ?>
					</ul>
				</div>
		        <?php } ?>
		            <div class="mob-filter-group">
		                <div class="filter-table-group">
		                    <label class="filter-label filter-label-radio">
		                        <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.price._]"<?php if (!$pricem2) { echo ' checked';} ?>>
		                    	<span class="filter-label-radio-btn"></span>
		                        <?php pll_e('Price'); ?>
		                    </label>
		                    <?php
		                    if ($pricem2) { ?>
		                        <input class="nna-change-pricetype-field-min filter-table-input" type="text" name="filters[transactions.transaction.pricem2._][min]" value="<?php echo $queryData['transactions.transaction.pricem2._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
		                        <?php
		                    } else {
		                        ?>
		                        <input class="nna-change-pricetype-field-min filter-table-input" type="text" name="filters[transactions.transaction.price._][min]" value="<?php echo $queryData['transactions.transaction.price._']['min']; ?>" placeholder="<?php pll_e('Price from'); ?>">
		                        <?php
		                    } ?>
		                </div>
		                <span class="filter-divider">-</span>
		                <div class="filter-table-group filter-table-group-right">
		                    <label class="filter-label filter-label-radio">
		                        <input class="nna-change-pricetype filter-radio-input" name="nna-change-pricetype" type="radio" value="filters[transactions.transaction.pricem2._]"<?php if ($pricem2) { echo ' checked';} ?>>
		                    	<span class="filter-label-radio-btn"></span>
		                        <?php pll_e('Price per square metre (filter)'); ?>
		                    </label>
		                    <?php
		                    if ($pricem2) { ?>
		                        <input class="nna-change-pricetype-field-max filter-table-input" type="text" name="filters[transactions.transaction.pricem2._][max]" value="<?php echo $queryData['transactions.transaction.pricem2._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
		                        <?php
		                    } else {
		                        ?>
		                        <input class="nna-change-pricetype-field-max filter-table-input" type="text" name="filters[transactions.transaction.price._][max]" value="<?php echo $queryData['transactions.transaction.price._']['max']; ?>" placeholder="<?php pll_e('Price to'); ?>">
		                        <?php
		                    }
		                    ?>
		                </div>
		                <div class="mob-filter-group">
		                    <label class="filter-label"><?php pll_e('Size'); ?></label>
		                    <div class="filter-table-group">
		                        <input class="filter-table-input" name="filters[areaSize._][min]" value="<?php echo $queryData['areaSize._']['min']; ?>" placeholder="<?php pll_e('Size from'); ?>">
		                    </div>
		                    <span class="filter-divider">-</span>
		                    <div class="filter-table-group  filter-table-group-right">
		                        <input class="filter-table-input" name="filters[areaSize._][max]" value="<?php echo $queryData['areaSize._']['max']; ?>" placeholder="<?php pll_e('Size to'); ?>">
		                    </div>
		                </div>
		            </div>

		            <div class="mob-filter-group">
		                <label class="filter-label"><?php pll_e('Keyword'); ?></label>
		                <input class="filter-table-input" name="filters[keyWord]" value="<?php echo $queryData['keyWord']; ?>" placeholder="<?php pll_e('Street, object ID, broker, etc'); ?>">
		            </div>
		        <?php
		            $options = $alacrity['data'];
                    usort($options, function($a, $b){
                        return strcmp(NnaTranslations::getInstance()->__($a->_id), NnaTranslations::getInstance()->__($b->_id));
                    });
		            $field = 'alacrity';
		            $selectedValue = '';
		            foreach ($options as $option) {
		                if ($option->_id == $queryData[$field]) {
		                    $selectedValue = $option->_id;
		                }
		            }
		        ?>

		            <div class="nna-dropdown dropdown-filter dropdown">
		                <input value="<?php echo $selectedValue; ?>" name="filters[<?php echo $field; ?>]" hidden>
		                <label class="filter-label"><?php echo $alacrity['label'] ?></label>
		                <button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
		                    <?php
		                    if ($selectedValue) {
		                        echo NnaTranslations::getInstance()->__($selectedValue);
		                    } else{
		                        pll_e('All');
		                    }
		                    ?>
		                    <i class="dropdown-filter-btn-icon"></i>
		                </button>
		                <ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
		                    <li class="nna-filter-option filter-dropdown-option" value="" text=""><?php pll_e('All'); ?></li>
		                    <?php foreach ($options as $option) { ?>
		                        <li class="nna-filter-option filter-dropdown-option" filter-value="<?php echo $option->_id; ?>"><?php echo $translator->__($option->_id); ?></li>
		                    <?php } ?>
		                </ul>
		            </div>
		            <div class="mob-filter-group">
		                <label class="filter-label"><?php pll_e('Floor'); ?>:</label>
		                <div class="filter-table-group">
		                    <input class="filter-table-input" type="text" name="filters[floorNumber][min]" value="<?php echo $queryData['floorNumber']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
		                </div>
		                    <span class="filter-divider">-</span>
		                <div class="filter-table-group filter-table-group-right">
		                    <input class="filter-table-input" type="text" name="filters[floorNumber][max]" value="<?php echo $queryData['floorNumber']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
		                </div>
		            </div>
		            <div class="mob-filter-group">
		                <label class="filter-label"><?php pll_e('Number of rooms'); ?>:</label>
		                <div class="filter-table-group">
		                    <input class="filter-table-input" type="text" name="filters[numberOfRooms][min]" value="<?php echo $queryData['numberOfRooms']['min']; ?>" placeholder="<?php pll_e('From'); ?>">
		                </div>
		                    <span class="filter-divider">-</span>
		                <div class="filter-table-group filter-table-group-right">
		                    <input class="filter-table-input" type="text" name="filters[numberOfRooms][max]" value="<?php echo $queryData['numberOfRooms']['max']; ?>" placeholder="<?php pll_e('To'); ?>">
		                </div>
		            </div>
		            <div class="mob-filter-group text-center mob-filter-group-wrap">
		                <button type="submit" class="filter-submit-btn"><?php pll_e('Find objects'); ?></button>
		            </div>
		    </form>
		</div>

		<div class="products nna-objects-container">
			<?php
	        $condition = array();
	        $options = array(
            	'limit' => 9 * $page,
	            'skip' => 0,
	            'sort' => NnaApi::getInstance()->handleSort($sort),
	        );
	        if (isset($_GET['favourites']) && $_GET['favourites'] == 1) {
	        	if ($favourites){
		        	$condition = array(
		        		'Object.originalId' => array(
		        			'$in' => array(
	        				),
	        			),
		        	);
			        $options['limit'] = 0;

		        	foreach ($favourites as $itemId) {
		        		array_push($condition['Object.originalId']['$in'], (int)$itemId);
		        	}
	        	}
	        } else if (isset($_GET['filters'])) {
	            $condition = NnaApi::getInstance()->handleObjectFilters($_GET['filters']);
	        }

			if (!is_user_logged_in()) {
				$condition['Object.hidden'] = array(
					'$ne' => 1,
				);
			}
	        $count = trimTrailingZeroes(number_format((float)NnaApi::getInstance()->getObjectsCount($condition), 2, '.', ' '));
	        // print_r($condition);
	        // print_r($options);
			?>

			<div class="products-filter products-filter-bottom clearfix">
			    <div class="products-filter-pages products-filter-pages-top">
			        <h1><?php pll_e('Realestate offers'); ?></h1><?php pll_e('In Total'); ?>: <strong><?php echo $count; ?></strong>
			    </div>
			    <ul class="display-type hidden-xs">
			    	<li class="display-type-btn js-products-display-type-link" identifier="list"><i class="display-icon bars-icon"></i></li>
			    	<li class="display-type-btn boxes-display-type-btn js-products-display-type-link " identifier="grid"><i class="display-icon boxes-icon"></i></li>
			    </ul>
				<div class="nna-objects-order products-filter-misc nna-dropdown dropdown-filter dropdown">
			    	<button class="btn nna-dropdown-btn dropdown-filter-btn dropdown-toggle products-filter-misc-sort offers-products-filter-misc-sort"  type="button" id="dropdownMenu2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
						<?php
						foreach (NnaApi::getInstance()->sortableFields() as $field => $values) {
							foreach ($values as $key => $value) {
								if ($field == $sortKey && $sort[$sortKey] == $key) {
									echo $value;
								}
							}
						}
						?>
						<i class="dropdown-filter-btn-icon"></i>
			    	</button>
			    	<ul class="dropdown-menu filter-dropdown-menu" aria-labelledby="dropdownMenu2">
			    	<?php
			    	foreach (NnaApi::getInstance()->sortableFields() as $field => $values) {
			    		foreach ($values as $key => $value) {
			    			echo '<li class="nna-sort-selector filter-dropdown-option" val="'.$field.':'.$key.'"';
			    			if ($field == $sortKey && $sort[$sortKey] == $key) {
			    				echo ' selected';
			    			}
			    			echo '>'.$value.'</li>';
			    		}
			    	}
			    	?>
			    	</ul>
			    </div>
			</div>

			<div class="nna-favourited-items favourited-items-btn <?php echo (count($favourites) > 0 ? 'active' : ''); ?>">
				<span class="favorited-icon"></span>
				<span class="nna-favourited-items-btn nna-favourite favourited-count"><?php echo count($favourites); ?></span>
				<span class="favourited-text"><?php pll_e('Favourited'); ?></span>
			</div>

			<div class="js-products-display-type-content products-content" identifier="grid">
				<div class="row nna-objects-grid">
					<?php
					$filters = array();
					if (isset($_GET) && !empty($_GET)) {
						$filters = $_GET;
					}
			        $data = NnaApi::getInstance()->getObjects($condition, $options);
			        $objects = NnaApi::getInstance()->fixObjects($data, false, $options['skip']);
			        // print_r($objects);
			        if ($objects) {
						foreach ($objects as $dataItem) {
							$object = $dataItem->Object;
							$filters['offset'] = $object->offset;
							?>
							<div class="col-sm-4">
							    <div class="offer">
							        <a href="<?php echo $object->url . '/' . ($filters ? '?' . http_build_query($filters) : ''); ?>"  class="offer-link">
							            <div class="offer-thumbnail">
							            	<div class="offer-thumbnail-wrap">
							            		<div class="offer-thumbnail-img-wrap">
							            			<?php
								            			if ($object->isBooked) {
								            			    echo '<div class="offer-thumbnail-booked">' . pll__('Booked until (objects view)') . ' ' . $object->bookedUntil . '</div>';
								            			}

														$curLang = pll_current_language();
														$images = get_field('images', $dataItem->PostIds->$curLang);
														if ($images) {
															$firstImage = $images[0]['image'];
															echo '<img class="offer-thumbnail-img" src="' . $firstImage['sizes']['large'] . '" alt="' . $firstImage['alt'] . '">';
														} else{
															echo '<img class="offer-thumbnail-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
														}
													?>

									                <?php if ($object->Country) { ?>
									                	<div class="offer-thumbnail-info"><?php echo $object->transactions->transaction->type . ', ' . $object->objectTypes->type; ?></div>
								                	<?php } ?>
						                		</div>
						                	</div>
							            </div>
							            <div class="offer-name list-offer-name">
							            <?php
							            	echo $object->title;
							            ?>
							            </div>
							            <ul class="offer-details">
							            	<li class="offer-details-item">
						            			<?php
						            			if ($object->numberOfRooms) {
						            				echo $object->numberOfRooms . ' ' . pll__('Rooms (objects view)');
						            			} else{
						            				echo '- ' . pll__('Rooms (objects view)');
						            			}
						            			?>
						            		</li>

						            		<li class="offer-details-item">
						            		    <?php
						            		    if ($object->areaSize && !$object->areaSize->_ == 0) {
						            		    	echo $object->areaSize->_ . ' m²';
						            			} else{
						            				echo '- m²';
						            			}
						            			?>
						            		</li>

						            		<li class="offer-details-item">
						            		    <?php
						            		    if ($object->floorNumber) {
						            		    	echo $object->floorNumber; ?>/<?php echo $object->numberOfFloors . ' ' . pll__('Floor (objects view)');
						            		    }
						            		    ?>
						            		</li>
							            </ul>
							            <?php if ($object->transactions->transaction->price){ ?>
								            <div class="offer-price offer-price-bg js-offer-price">
								                <div class="offer-price-amount list-offer-price-amount">
								                    <?php echo $object->transactions->transaction->price->_. ' ' . pll__('(Currency symbol)'); ?>
								                    <?php if ($object->transactions->transaction->pricem2) { ?>
									                    <div class="offer-price-details-extra">
					                                    	<?php echo '(' . $object->transactions->transaction->pricem2->_ . ' ' . pll__('(Currency symbol)') . '/m²)'; ?>
									                    </div>
							            			<?php } ?>
							            			<div class="nna-add-favourite offer-price-favorite hidden-print js-offer-price-favorite <?php echo (in_array($object->originalId, $favourites) ? 'active' : ''); ?>" item-id="<?php echo $object->originalId; ?>"></div>
								                </div>
								            </div>
							            <?php } ?>
							        </a>
							    </div>
							</div>
							<div class="col-xs-12 object-seperator"></div>
							<?php
						}

					} else{
						echo '<div class="offer-no-found text-center">' . pll__('No objects found') . '</div>';
					}
					unset($filters['offset']);
					?>
				</div>
			</div>
			<div class="js-products-display-type-content products-content hidden-print hidden-xs" identifier="list">
				<?php
					if ($objects) {
				?>
					<table class="table table-offer">
						<thead>
							<th></th>
							<th><?php pll_e('Total area (list view)'); ?></th>
							<th><?php pll_e('Rooms (list view)'); ?></th>
							<th><?php pll_e('Floor (list view)'); ?></th>
							<th><?php pll_e('Price per square metre (list view)'); ?></th>
							<th><?php pll_e('Price'); ?></th>
						</thead>
						<tbody class="nna-objects-list block-offer-list">
							<?php
								foreach ($objects as $dataItem) {
									$object = $dataItem->Object;
									$filters['offset'] = $object->offset;
									?>
									<tr>
										<td class="block-offer-img-td" rowspan="3">
											<a href="<?php echo $object->url . '/' . ($filters ? '?' . http_build_query($filters) : ''); ?>" class="block-offer-thumbnail">
												<div class="block-offer-thumbnail-img-wrap">
													<div class="offer-thumbnail-img-wrap">
														<?php
															if ($object->isBooked) {
															    echo '<div class="offer-thumbnail-booked">' . pll__('Booked until (objects view)') . ' ' . $object->bookedUntil . '</div>';
															}
															$curLang = pll_current_language();
															$images = get_field('images', $dataItem->PostIds->$curLang);
															if ($images) {
																$firstImage = $images[0]['image'];
																echo '<img class="block-offer-thumbnail-img" src="' . $firstImage['sizes']['large'] . '" alt="' . $firstImage['alt'] . '">';
															} else{
																echo '<img class="block-offer-thumbnail-img" src="' . get_template_directory_uri() . '/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">';
															}
														?>
													</div>
												</div>
								                <?php if ($object->Country) { ?>
								                	<div class="offer-thumbnail-info"><?php echo $object->transactions->transaction->type . ', ' . $object->objectTypes->type; ?></div>
							                	<?php } ?>
											</a>
										</td>
										<td style="position:relative;" colspan="5">
											<div class="block-offer-name">
												<a class="block-offer-name-link" href="<?php echo $object->url . '/' . ($filters ? '?' . http_build_query($filters) : ''); ?>">
													<?php
														echo $object->title;
													?>
												</a>
											</div>
											<div class="nna-add-favourite offer-price-favorite js-offer-price-favorite <?php echo (in_array($object->originalId, $favourites) ? 'active' : ''); ?>" item-id="<?php echo $object->originalId; ?>"></div>
										</td>
									</tr>
									<tr class="block-offer-details-row">
										<td>
			                                <?php
			                                if ($object->areaSize && !$object->areaSize->_ == 0) {
			                                	echo $object->areaSize->_ . ' m²';
			                            	} else{
			                            		echo '- m²';
			                            	}
			                            	?>
										</td>
										<td>
											<?php
											if ($object->numberOfRooms) {
												echo $object->numberOfRooms . ' ' . pll__('Rooms (objects view)');
											} else{
												echo '- ' . pll__('Rooms (objects view)');
											}
											?>
										</td>
										<td>
											<?php
											if ($object->floorNumber) {
												echo $object->floorNumber; ?>/<?php echo $object->numberOfFloors . ' ' . pll__('Floor (objects view)');
											}
											?>
										</td>
										<td>
						                    <?php if ($object->transactions->transaction->pricem2) { ?>
			                                    <?php echo '(' . $object->transactions->transaction->pricem2->_ . ' ' . pll__('(Currency symbol)') . '/m²)'; ?>
					            			<?php } ?>
										</td>
										<td>
											<div class="offer-price-amount block-offer-price-amount">
												<?php echo $object->transactions->transaction->price->_ . ' ' . pll__('(Currency symbol)'); ?>
											</div>
										</td>
									</tr>
									<tr>
										<td colspan="5">
											<?php if ($object->transactions->transaction->infoExcerpt){
											    foreach ($object->transactions->transaction->infoExcerpt as $info){
											        if ($info->language == NnaApi::getInstance()->getLanguageCode(pll_current_language()) && $info->_){ ?>
											            <div class="block-offer-content-text">
											            	<?php
											            		echo $info->_;
											            		// $excerpt = $info->_;
											            		// $charlength = 340;
												            	// if ( mb_strlen( $excerpt ) > $charlength ) {
												            	// 	$subex = mb_substr( $excerpt, 0, $charlength - 5 );
												            	// 	$exwords = explode( ' ', $subex );
												            	// 	$excut = - ( mb_strlen( $exwords[ count( $exwords ) - 1 ] ) );
												            	// 	if ( $excut < 0 ) {
												            	// 		echo mb_substr( $subex, 0, $excut );
												            	// 	} else {
												            	// 		echo $subex;
												            	// 	}
												            	// 	echo '...';
												            	// } else {
												            	// 	echo $excerpt;
												            	// }
											            	?>
											            </div>
											        <?php
											        }
											    }
											} ?>
										</td>
									</tr>
								<?php
								}
							?>
						</tbody>
					</table>
				<?php
					}else{
						echo '<div class="offer-no-found text-center">' . pll__('No objects found') . '</div>';
					}
					unset($filters['offset']);
				?>
			</div>

			<div class="nna-loading-product products-filter products-filter-top clearfix">
			    <div class="products-filter-pages">
			    	<?php pll_e('Objects found in Total'); ?>: <strong><?php echo $count; ?> </strong>
			    </div>
			    <div class="products-filter-more hidden">
			    	<div class="nna-load-objects products-filter-more-button" data-page="2"><?php pll_e('Load more objects'); ?></div>
			    </div>
			    	<a href="#" class="products-secondary-button"><?php pll_e('Back to top'); ?></a>
			</div>
		</div>


	</div>

	<script type="text/html" id="objectListGrid">
		{{#objects}}
            <div class="col-sm-4">
                <div class="offer">
                    <a href="{{url}}?offset={{offset}}/<?php echo ($filters ? '&' . http_build_query($filters) : ''); ?>" class="offer-link">
                    	<div class="offer-thumbnail">
                    		<div class="offer-thumbnail-wrap">
                    			<div class="offer-thumbnail-img-wrap">
                    				{{#isBooked}}
                    				    <div class="offer-thumbnail-booked"><?php pll_e('Booked until (objects view)')?> {{bookedUntil}}</div>
                    				{{/isBooked}}

		                        	{{#thumbnail}}
		                    			<img class="offer-thumbnail-img" src="{{thumbnail.sizes.large}}" alt="{{thumbnail.alt}}">
		                        	{{/thumbnail}}
		                        	{{^thumbnail}}
		                        		<img class="offer-thumbnail-img" src="<?php echo get_template_directory_uri(); ?>/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">
		                        	{{/thumbnail}}
		                            {{#transactions.transaction.type}}
		                            	<div class="offer-thumbnail-info"> {{transactions.transaction.type}}{{#objectTypes.type}}, {{objectTypes.type}}{{/objectTypes.type}}</div>
		                            {{/transactions.transaction.type}}
	                            </div>
	                        </div>
	                    </div>
                        <div class="offer-name list-offer-name">
                            {{title}}
                        </div>
                        <ul class="offer-details">
                        	{{#numberOfRooms}}
                            	<li class="offer-details-item">{{numberOfRooms}} <?php pll_e('Rooms (objects view)'); ?></li>
                        	{{/numberOfRooms}}
                        	{{^numberOfRooms}}
                        	 	<li class="offer-details-item">- <?php pll_e('Rooms (objects view)'); ?></li>
                        	{{/numberOfRooms}}
                            {{#areaSize}}
                            	<li class="offer-details-item">{{areaSize._}} m²</li>
                            {{/areaSize}}
                            {{^areaSize}}
                            	<li class="offer-details-item">- m²</li>
                            {{/areaSize}}
                            {{#floorNumber}}
                            	<li class="offer-details-item">{{floorNumber}}/{{numberOfFloors}} <?php pll_e('Floor (objects view)'); ?></li>
                            {{/floorNumber}}
                        </ul>
                        {{#transactions.transaction.price}}
	                        <div class="offer-price offer-price-bg js-offer-price">
	                            <div class="offer-price-amount list-offer-price-amount">
	                                {{transactions.transaction.price._}} <?php pll_e('(Currency symbol)'); ?>
	                                <div class="offer-price-details-extra">
	                                	({{transactions.transaction.pricem2._}} <?php pll_e('(Currency symbol)'); ?>/m²)
	                                </div>
	                                <div class="nna-add-favourite offer-price-favorite js-offer-price-favorite" item-id="{{originalId}}"></div>
	                            </div>
	                        </div>
                        {{/transactions.transaction.price}}
                    </a>
                </div>
            </div>
            <hr class="col-xs-12 object-seperator">
        {{/objects}}
	</script>

	<script type="text/html" id="objectListList">
		{{#objects}}
			<tr>
				<td class="block-offer-img-td" rowspan="3">
					<a href="{{url}}?offset={{offset}}/<?php echo ($filters ? '&' . http_build_query($filters) : ''); ?>" class="block-offer-thumbnail">
						<div class="block-offer-thumbnail-img-wrap">
							<div class="offer-thumbnail-img-wrap">
								{{#isBooked}}
								    <div class="offer-thumbnail-booked"><?php pll_e('Booked until (objects view)')?> {{bookedUntil}}</div>
								{{/isBooked}}
						    	{{#thumbnail}}
									<img class="block-offer-thumbnail-img" src="{{thumbnail.sizes.large}}" alt="{{thumbnail.alt}}">
						    	{{/thumbnail}}
						    	{{^thumbnail}}
						    		<img class="block-offer-thumbnail-img" src="<?php echo get_template_directory_uri(); ?>/assets/img/pindi_pakkumise_placeholder.jpg" alt="No image found">
						    	{{/thumbnail}}
						        {{#transactions.transaction.type}}
						        	<div class="offer-thumbnail-info"> {{transactions.transaction.type}}{{#objectTypes.type}}, {{objectTypes.type}}{{/objectTypes.type}}</div>
						        {{/transactions.transaction.type}}
							</div>
						</div>
					</a>
				</td>
				<td style="position:relative;" colspan="5">
					<div class="block-offer-name">
						<a class="block-offer-name-link" href="{{url}}?offset={{offset}}/<?php echo ($filters ? '&' . http_build_query($filters) : ''); ?>">
							{{title}}
						</a>
						<div class="nna-add-favourite offer-price-favorite js-offer-price-favorite" item-id="{{originalId}}"></div>
					</div>
				</td>
			</tr>
			<tr class="block-offer-details-row">
				<td>
					{{#areaSize}}
						{{areaSize._}} m²
					{{/areaSize}}
					{{^areaSize}}
						- m²
					{{/areaSize}}
				</td>
				<td>
					{{#numberOfRooms}}
				    	{{numberOfRooms}} <?php pll_e('Rooms (objects view)'); ?>
					{{/numberOfRooms}}
					{{^numberOfRooms}}
					 	- <?php pll_e('Rooms (objects view)'); ?>
					{{/numberOfRooms}}
				</td>
				<td>
					{{#floorNumber}}
						{{floorNumber}}/{{numberOfFloors}} <?php pll_e('Floor (objects view)'); ?>
					{{/floorNumber}}
				</td>
				<td>
                    {{#transactions.transaction.pricem2}}
                    	({{transactions.transaction.pricem2._}} <?php pll_e('(Currency symbol)'); ?>/m²)
                    {{/transactions.transaction.pricem2}}
				</td>
				<td>
                    {{#transactions.transaction.price}}
                    	<div class="offer-price-amount block-offer-price-amount">
                        {{transactions.transaction.price._}} <?php pll_e('(Currency symbol)'); ?>
                       	</div>
                    {{/transactions.transaction.price}}
				</td>
			</tr>
			<tr>
				<td colspan="5">
					{{#transactions.transaction.infoStripped}}
			            <div class="block-offer-content-text">
		            		{{transactions.transaction.infoExcerpt.<?php echo pll_current_language(); ?>._}}
			            </div>
					{{/transactions.transaction.infoStripped}}
				</td>
			</tr>
		{{/objects}}
	</script>
	<script type="text/javascript">
		var favourites = parseInt(getUrlParameter('favourites'));

		$(window).on('scroll', function(){
			if (!favourites) {
				var el = $('.nna-objects-container');
				var elBot = el.offset().top + el.height();
				var viewportBot = $(window).scrollTop() + $(window).height();
				if (elBot <= viewportBot) {
					if (!$('.nna-load-objects').hasClass('loading')) {
						$('.nna-load-objects').trigger('click');
					}
				}
			}
		});

		function getUrlParameter(sParam) {
		    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
		        sURLVariables = sPageURL.split('&'),
		        sParameterName,
		        i;

		    for (i = 0; i < sURLVariables.length; i++) {
		        sParameterName = sURLVariables[i].split('=');

		        if (sParameterName[0] === sParam) {
		            return sParameterName[1] === undefined ? true : sParameterName[1];
		        }
		    }
		}
	</script>

	<?php get_template_part('blocks/b_footer'); ?>

</body>
</html>
