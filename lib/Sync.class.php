<?php
abstract class Sync {
    const SCORO = 1;
    const BRENOLLIS = 2;

    abstract public static function getInstance();
    abstract public function syncUsers();
    abstract public function startObjectSync($all = false);

    public function fixMissingBrokerPosts()
    {
        $wpHandler = WpHandler::getInstance();
        $api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName().'.brokers');

        $totalCount = 0;
        $fixedCount = 0;
        $params = [];
        // $params = ['Metainfo.source' => BrenollisToScoroMapper::SOURCE];
        foreach ($api->getBrokers($params) as $broker) {
            $missingPosts = [];
            foreach($broker->PostIds as $lang => $postId) {
                $post = get_post($postId);
                if (!$post) {
                    $missingPosts[] = $lang;
                }
            }
            if (count($missingPosts)) {
                echo 'missing '.$broker->Broker->name.' '.$broker->Broker->originalId.PHP_EOL;
                $objects = $api->getObjects(['Object.Brokers.originalId' => $broker->Broker->originalId]);
                echo 'objects: '.count($objects).PHP_EOL;
                $foundAll = true;
                $postsByLanguage = (array) $broker->PostIds;
                foreach ($missingPosts as $language) {
                    $posts = $wpHandler->getBrokersByEmail($broker->Broker->email, [$language]);
                    if (count($posts) === 1) {
                        $postsByLanguage[$language] = $posts[0]->ID;
                    } else {
                        // $posts = $wpHandler->getBrokersByMobile($broker->Broker->mobile, [$language]);
                        // if (count($posts) === 1) {
                        //     $postsByLanguage[$language] = $posts[0]->ID;
                        // } else {
                        //     $foundAll = false;
                        // }
                        $foundAll = false;
                    }
                }
                if ($foundAll) {
                    echo $broker->Broker->email;
                    // print_r($postsByLanguage);
                    $collection->updateOne(
                        array('_id' => $broker->_id),
                        array(
                            '$set' => array(
                                'PostIds' => $postsByLanguage,
                            )
                        )
                    );
                    echo 'fixed '.$broker->Broker->originalId.PHP_EOL;
                    $fixedCount++;
                }
                $totalCount++;
            }
        }
        echo 'missing total: ' . $totalCount.PHP_EOL;
        echo 'fixed total: ' . $fixedCount.PHP_EOL;
    }

    public function getObjectTitles($object)
    {
    	$api = NnaApi::getInstance();
        $ret = array();
        // $translator = NnaTranslations::getInstance();
        $address = $api->getAddress($object);
        foreach (NnaApi::getInstance()->getLanguages() as $code) {
            // $title = str_replace(NnaTranslations::$cyrillicMap, NnaTranslations::$latinMap, $translator->translateString($object->objectTypes->type, $code)).$address;
            $title = $address;
            $ret[$code] = $title;
        }
        return $ret;
    }

    /**
     *
     *
     * @param $object
     * @param $collection
     * @return array|bool
     */
    public function insertObject($object, $collection)
    {
    	$api = NnaApi::getInstance();
        $broker = NnaApi::getInstance()->getBrokers(array(
            'Broker.originalId' => (int)$object->Object->Brokers->originalId,
        ));
        $object->Object->brokerName = '';
        if (isset($broker[0])) {
            $object->Object->brokerName = $broker[0]->Broker->name;
        }
        $object->Object->title = $this->getObjectTitles($object->Object);
        $object->Object->fullAddress = $api->getAddress($object->Object);
        // Reduced memory logging for performance
        if (rand(1, 50) == 1) { // Log memory only 1 in 50 times
            file_put_contents(NNA_PATH . '/log/synclog.log', memory_get_usage().PHP_EOL, FILE_APPEND);
        }

        $data = WpHandler::getInstance()->insertObjectToWp($object->Object);
        if ($data) {
            $data['objectId'] = $object->Object->originalId;

            $collection->insertOne(array(
                'PostIds' => $data['ids'],
                'Images' => array_map(
                	function($item) {
	                    return array(
	                        'hash' => $item->post_title,
	                    );
	                },
	                $data['images']
                ),
                'Object' => $object->Object,
                'Metainfo' => $object->Metainfo,
            ));
            // Reduced memory logging for performance
            if (rand(1, 50) == 1) { // Log memory only 1 in 50 times
                file_put_contents(NNA_PATH . '/log/synclog.log', 'inserted. Memory: '.memory_get_usage().PHP_EOL, FILE_APPEND);
            }
        }

        return $data;
    }

    public function updateContent($object, $ids)
    {   
        foreach ($ids as $lang => $id) {
            foreach ($object->transactions->transaction->info as $info){
                if ($info->language == NnaApi::getInstance()->getLanguageCode($lang) && $info->_){
                    $content = $info->_;
                    $patterns = array(
                        '---<br />
Pindi Kinnisvara on Eesti suurim kinnisvarabüroo, millel on 18 esindust üle Eesti. www.pindi.ee. Meie andmebaasis on üle 3000 aktiivse pakkumise (sh üle 600 äripinna) ning aastas hindame u 5000 kinnisvaraobjekti. Meie haldus-hooldusfirma portfellis on ligi 200 korterelamut ja ärihoonet, grupi kindlustusettevõttel on 5000 klienti.',
                        'Pindi Kinnisvara on Eesti suurim kinnisvarabüroo, millel on 18 esindust üle Eesti. www.pindi.ee. Meie andmebaasis on üle 3000 aktiivse pakkumise (sh üle 600 äripinna) ning aastas hindame u 5000 kinnisvaraobjekti. Meie haldus-hooldusfirma portfellis on ligi 200 korterelamut ja ärihoonet, grupi kindlustusettevõttel on 5000 klienti.',
                    );
                    foreach ($patterns as $pattern) {
                        $content = str_replace($pattern, '', $content);
                    }
                    $my_post = array(
                        'ID'           => $id,
                        'post_content' => $content,
                    );
                    wp_update_post($my_post);
                }
            }
        }
    }

    public function updateGoogleMapFields($object, $ids)
    {
        foreach ($ids as $id) {
            WpHandler::getInstance()->populateGoogleMapField($id, $object);
        }
    }

    public function updateObject($new, $old, $collection)
    {   
        $api = NnaApi::getInstance();

        if ($new->Object->originalId === 10317) {
            //var_dump($old->_id);
            $api->setBrokers($old, ['originalId' => (int)$new->Object->Brokers->originalId+100000]);
            //var_dump($old->_id);
            //$api->setBrokerPostIds($new, $old->PostIds);
            //var_dump($new);
        }
        
        file_put_contents(NNA_PATH . '/log/synclog.log', 'needs updating ... '. PHP_EOL, FILE_APPEND);
        $broker = NnaApi::getInstance()->getBrokers(array(
            'Broker.originalId' => (int)$new->Object->Brokers->originalId,
        ));
        //var_dump($broker);

        $new->Object->brokerName = $broker[0]->Broker->name;
        $new->Object->title = $this->getObjectTitles($new->Object);
        $new->Object->fullAddress = $api->getAddress($new->Object);

        $oldImages = array();
        if (isset($old->Images)) {
            $oldImages = $old->Images;
        }

        file_put_contents(NNA_PATH . '/log/synclog.log', $new->Object->originalId.' '.date('d-m-Y H:i-s').' '.memory_get_usage(), FILE_APPEND);
        $images = array_map(function($item) {
            return array(
                'hash' => $item->post_title,
            );
        },
        $this->updateObjectImages($new->Object, $oldImages, (array)$old->PostIds));
        // file_put_contents(NNA_PATH . '/log/synclog.log', ' '.memory_get_usage(), FILE_APPEND);
        $this->updateGoogleMapFields($new->Object, (array)$old->PostIds);
        // file_put_contents(NNA_PATH . '/log/synclog.log', ' '.memory_get_usage(), FILE_APPEND);
        $this->updateContent($new->Object, (array)$old->PostIds);
        // file_put_contents(NNA_PATH . '/log/synclog.log', ' '.memory_get_usage(), FILE_APPEND);
        $this->updateTitles($new->Object, (array)$old->PostIds);
        // file_put_contents(NNA_PATH . '/log/synclog.log', ' '.memory_get_usage(), FILE_APPEND);
        if ($collection->updateOne(array('_id' => $old->_id), array(
            '$set' => array(
                'Object' => $new->Object,
                'Images' => $images,
            )
        ))) {
            file_put_contents(NNA_PATH . '/log/synclog.log', ' updated '.$new->Object->originalId. PHP_EOL, FILE_APPEND);
        }
        
        // file_put_contents(NNA_PATH . '/log/synclog.log', ' '.memory_get_usage().PHP_EOL, FILE_APPEND);
    }

    public function updateObjectImages($object, $imageMap, $ids)
    {
        $newImages = array();
        $flip = array();
        // $flip = array_flip((array)$imageMap);
        foreach ($imageMap as $key => $value) {
            $flip[$value->hash] = $key;
        }

        if (is_array($object->pictures->pictureUrl)) {
            $pictures = $object->pictures->pictureUrl;
        } else {
            $pictures = array($object->pictures->pictureUrl);
        }

        foreach ($pictures as $image) {
            if (isset($image->hash) && isset($flip[$image->hash])) {
                unset($flip[$image->hash]);
            } else {
                $newImages[] = $image;
            }
        }

        foreach ($flip as $attachmentId) {
            wp_delete_attachment($attachmentId, true);
        }
        $attachments = WpHandler::getInstance()->insertImages($newImages, $ids[pll_default_language()]);

        $attachmentsWithHashKeys = array();
        foreach ($attachments as $attachment) {
            $attachmentsWithHashKeys[$attachment->post_title] = $attachment;
        }

        $attachmentsOrdered = array();
        foreach ($pictures as $image) {
            $attachmentsOrdered[] = $attachmentsWithHashKeys[$image->hash];
        }

        foreach ($ids as $id) {
            WpHandler::getInstance()->populateImageFields($id, $attachmentsOrdered);
        }
        return $attachments;
    }

    public function updateTitles($object, $ids)
    {
        $titles = $this->getObjectTitles($object);
        // print_r($titles);
        foreach ($ids as $lang => $id) {
            foreach ($object->transactions->transaction->info as $info){
                if ($info->language == NnaApi::getInstance()->getLanguageCode($lang) && $info->_){
                    $my_post = array(
                        'ID'           => $id,
                        'post_title' => $titles[$lang],
                    );
                    wp_update_post($my_post, true);
                }
            }
        }
    }


    public function getCountyNameMapper() {
        return CountyNameMapper::getInstance();
    }

    public function getCityNameMapper() {
        return CityNameMapper::getInstance();
    }
}

abstract class BaseNameMapper {
    protected $namesMap = [];

    public function mapName($name) {
        if (!isset($this->namesMap[$name])) {
            return $name;
        }
        return $this->namesMap[$name];
    }
}

class CountyNameMapper extends BaseNameMapper {
    protected $namesMap = [
        'Harjumaa' => 'Harju maakond',
        'Hiiumaa' => 'Hiiu maakond',
        'Ida-Virumaa' => 'Ida-Viru maakond',
        'Järvamaa' => 'Järva maakond',
        'Jõgevamaa' => 'Jõgeva maakond',
        'Läänemaa' => 'Lääne maakond',
        'Lääne-Virumaa' => 'Lääne-Viru maakond',
        'Pärnumaa' => 'Pärnu maakond',
        'Põlvamaa' => 'Põlva maakond',
        'Raplamaa' => 'Rapla maakond',
        'Saaremaa' => 'Saare maakond',
        'Tartumaa' => 'Tartu maakond',
        'Valgamaa' => 'Valga maakond',
        'Viljandimaa' => 'Viljandi maakond',
        'Võrumaa' => 'Võru maakond',
    ];

    private static $instance;

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }
}

class CityNameMapper extends BaseNameMapper {
    protected $namesMap = [

    ];

    private static $instance;

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }
}
