<?php
class NnaHiddenObjectUpdater
{
	private static $instance;
    protected $_manager;
    protected $_dbName;
    protected $_languages;

	public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function getManager()
    {
        if (!$this->_manager) {
            $options = get_option(NnaSettings::$mongoSettingsName);
            $url = '';
            if (isset($options['dbUrl'])) {
                $url = $options['dbUrl'];
            }
            if (isset($options['user']) && isset($options['password'])) {
                $url = "mongodb://".urlencode($options['user']).":".urlencode($options['password'])."@".$url."/".urlencode($options['dbName'])."?authSource=test";
            } else {
                $url = 'mongodb://'.$url;
            }
            $this->_manager = new MongoDB\Driver\Manager($url);
        }
        return $this->_manager;
    }

    public function getCollection($name)
    {
        return new MongoDB\Collection($this->getManager(), $name);
    }

    public function getDbName()
    {
        if (!$this->_dbName) {
            $options = get_option(NnaSettings::$mongoSettingsName);
            if (!isset($options['dbName'])) {
                throw new Exception('Mongo database name not set');
            }
            $this->_dbName = $options['dbName'];
        }
        return $this->_dbName;
    }

	public function getObjectsWsdl()
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['specialUser']) || !isset($options['specialPassword'])) {
            throw new Exception('No username or password');
        }
        ini_set("soap.wsdl_cache_enabled", "0");
        $context = stream_context_create(array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ));
        return new RealEstateDataExchange(
            'https://versions.next.ee/_next_development/maintenance/RealEstate/REDE.wsdl',
            array(
                'login' => $options['specialUser'],
                'password' => $options['specialPassword'],
                'trace' => 1,
                'stream_context' => $context,
            )
        );
    }

    public function getWsdlParams($request)
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['specialUser']) || !isset($options['specialPassword'])) {
            throw new Exception('No username or password');
        }
        $params = new $request();
        $params->username = $options['specialUser'];
        $params->password = $options['specialPassword'];
        return $params;
    }

    public function startObjectSync()
    {
        file_put_contents(NNA_PATH . '/log/hiddensync.log', date('d-m-Y H:i-s').PHP_EOL, FILE_APPEND);

        $client = $this->getObjectsWsdl();

        $params = $this->getWsdlParams('getObjectIdsByStatus');
        $params->status = 'active';

        $ids = $client->getObjectIdsByStatus($params)->originalId;

        $ids = array();

        // Scoro forced my hand
        $culledIds = array();
        foreach ($ids as $id) {
            $culledIds[] = (int) $id;
            $culledIds[] = (int) ($id / 1000);
        }
        $culledIds = array_values(array_unique($culledIds));

        $collection = NnaApi::getInstance()->getCollection($this->getDbName().'.objects');

        $condition=array(
            'Object.originalId' => array(
                '$nin' => $culledIds,
            ),
            'Object.hidden' => 1
        );

        $collection->updateMany($condition, array(
        	'$set' => array(
        		'Object.hidden' => 0,
    		)
    	));

        $condition=array(
            'Object.originalId' => array(
                '$in' => $culledIds,
            ),
        );

        $collection->updateMany($condition, array(
        	'$set' => array(
        		'Object.hidden' => 1,
        	)
        ));
    }
}
