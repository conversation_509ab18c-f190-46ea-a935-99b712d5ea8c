<?php
class WpHandler {
	private static $instance;
	private $_adminLoaded = false;
	private $_imagesKey;
    private $_googleMapKey;

	public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }
        return self::$instance;
    }

    public static function handleScoroRedirect() {
        if (isset($_GET['action']) && $_GET['action'] == 'object' && isset($_GET['id'])) {
            $condition = array(
                'Object.originalId' => (int) $_GET['id'],
            );

            $postIds = NnaApi::getInstance()->getObjects($condition, array(
                'projection' => array(
                    'PostIds' => true,
                )
            ));
            if (isset($postIds[0]) && isset($postIds[0]->PostIds->et)) {
                wp_redirect(get_permalink($postIds[0]->PostIds->et));
                exit;
            }
        }
    }

    public function getFieldGroup($groupName)
    {
    	$acfs = acf_get_field_groups();
		foreach ($acfs as $acf) {
			if ($acf['title'] == $groupName) {
                return acf_get_fields($acf['ID']);
			}
		}
        return [];
    }

    public function getImagesKey()
    {
    	if (!$this->_imagesKey) {
    		foreach ($this->getFieldGroup('Object') as $field) {
    			if ($field['_name'] == 'images') {
    				$this->_imagesKey = $field['key'];
    				break;
    			}
    		}
    	}
    	return $this->_imagesKey;
    }

    public function getGoogleMapKey()
    {
        if (!$this->_googleMapKey) {
            foreach ($this->getFieldGroup('Object') as $field) {
                if ($field['_name'] == 'google_map_object') {
                    $this->_googleMapKey = $field['key'];
                    break;
                }
            }
        }
        return $this->_googleMapKey;
    }

    public function loadAdmin()
    {
    	if (!$this->_adminLoaded) {
            require_once(ABSPATH . 'wp-admin/includes/media.php');
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
            $this->_adminLoaded = true;
        }
    }

    public function insertImages($images, $postId)
    {
        $this->loadAdmin();
        if (!is_array($images)) {
            $images = array($images);
        }
        foreach ($images as $url) {
            if (!isset($url->_) || !getimagesize($url->_)) {
                continue;
            }
            media_sideload_image($url->_, $postId, $url->hash, 'src');
        }
        // reference new image to set as featured
        return get_attached_media('image', $postId);
    }

    public function insertBrokerToWp($object)
    {
        $this->loadAdmin();
        $ids = array();
        $translator = NnaTranslations::getInstance();
        foreach (NnaApi::getInstance()->getLanguages() as $code) {
            $postId = wp_insert_post(array(
                'post_title' => $object->name,
                'post_type' => NewtimeNextApi::$brokerPostType,
                'post_status' => 'publish',
            ), true);
            if (is_wp_error($postId)) {
                echo 'Failed to insert '.$object->originalId.PHP_EOL;
                foreach ($ids as $id) {
                    wp_delete_post($id, true);
                }
                print_r($postId);
                die();
                return false;
            }
            $this->fillBrokerCustomFields($postId, $object);
            pll_set_post_language($postId, $code);
            $ids[$code] = $postId;
        }
        pll_save_post_translations($ids);
        $this->insertFeaturedImages($ids, $object->pictureUrl, $object->name);
        return $ids;
    }

    public function updateBrokerPosts($oldObject, $newObject)
    {
        $api = NnaApi::getInstance();
        $changed = false;
        $postIds = array();
        foreach ($api->getLanguages() as $code) {
            if (isset($oldObject->PostIds->$code)) {
                $postId = $oldObject->PostIds->$code;
                $post = get_post($postId);
                if ($post) {
                    WpHandler::getInstance()->fillBrokerCustomFields($postId, $newObject->Broker);
                    $postIds[$code] = $postId;
                    continue;
                }
            }
            echo 'no post found '. $postId . ' creating new'.PHP_EOL;
            $postId = WpHandler::getInstance()->insertBrokerPost($newObject->Broker, $code);

            if ($postId) {
                $changed = true;
                $postIds[$code] = $postId;
            }
        }
        if ($changed) {
            echo 'updating postids'.PHP_EOL;
            $collection = $api->getCollection($api->getDbName().'.brokers');
            $collection->updateOne(array('_id' => $oldObject->_id), array(
                '$set' => array(
                    'PostIds' => $postIds,
                ),
            ));
            pll_save_post_translations($postIds);
            $this->insertFeaturedImages($postIds, $newObject->Broker->pictureUrl, $newObject->Broker->name);
        }
        return $postIds;
    }

    public function insertBrokerPost($object, $lang)
    {
        $this->loadAdmin();
        $postId = wp_insert_post(array(
            'post_title' => $object->name,
            'post_type' => NewtimeNextApi::$brokerPostType,
            'post_status' => 'publish',
        ), true);
        if (is_wp_error($postId)) {
            echo 'Failed to insert '.$object->originalId.' - '.$lang.PHP_EOL;
            print_r($postId);
            return false;
        }
        $this->fillBrokerCustomFields($postId, $object);
        pll_set_post_language($postId, $lang);
        return $postId;
    }

    public function fillBrokerCustomFields($postId, $object)
    {
        add_post_meta($postId, 'scoro_id', $object->originalId, true) || update_post_meta($postId, 'scoro_id', $object->originalId);
        add_post_meta($postId, 'phone_number', $object->phone, true) || update_post_meta($postId, 'phone_number', $object->phone);
        add_post_meta($postId, 'mobile_number', $object->mobile, true) || update_post_meta($postId, 'mobile_number', $object->mobile);
        add_post_meta($postId, 'email', $object->email, true) || update_post_meta($postId, 'email', $object->email);
    }

    public function insertFeaturedImages($postIds, $url, $desc = null)
    {
        if (!getimagesize($url)) {
            return;
        }
        $postId = $postIds;
        if (is_array($postIds)) {
            $postId = $postIds[pll_default_language()];
        }
        $media = media_sideload_image($url, $postId, $desc, 'src');
        if(!empty($media) && !is_wp_error($media)){
            $args = array(
                'post_type' => 'attachment',
                'posts_per_page' => -1,
                'post_status' => 'any',
                'lang' => '',
                'post_parent' => $postId
            );

            // reference new image to set as featured
            $attachments = get_posts($args);

            if(isset($attachments) && is_array($attachments)){
            	if (is_array($postIds)) {
            		foreach ($postIds as $code => $postId) {
	                    if (set_post_thumbnail($postId, $attachments[0]->ID)) {
	                        echo 'thumbnail set'.PHP_EOL;
	                    }
	                }
            	} else {
	                if (set_post_thumbnail($postId, $attachments[0]->ID)) {
	                    echo 'thumbnail set'.PHP_EOL;
	                }
            	}
            }
        }
    }

    public function insertFeaturedImage($postId, $url, $desc = null)
    {
    	$this->insertFeaturedImages($postId, $url, $desc);
    }

    public function deleteWpPosts($args = array())
    {
        if (!isset($args['posts_per_page'])) {
            $args['posts_per_page'] = '-1';
        }
        if (!isset($args['lang'])) {
            $args['lang'] = '';
        }
        if (!isset($args['post_status'])) {
            $args['post_status'] = 'draft,publish';
        }
        $posts = get_posts($args);
        foreach ($posts as $post) {
            echo 'deleting '.$post->ID.PHP_EOL;
            wp_delete_post($post->ID, true);
        }
    }

    public function getBrokersByEmail($email, $languages = [])
    {
        $lang = implode(',', $languages);
        $posts = get_posts(array(
            'numberposts'   => -1,
            'post_type'     => 'broker',
            'meta_key'      => 'email',
            'meta_value'    => $email,
            'lang'          => $lang,
        ));

        return $posts;
    }

    public function getBrokersByMobile($number, $languages = [])
    {
        $lang = implode(',', $languages);
        $posts = get_posts(array(
            'numberposts'   => -1,
            'post_type'     => 'broker',
            'meta_key'      => 'mobile_number',
            'meta_value'    => $number,
            'lang'          => $lang,
        ));

        return $posts;
    }

    public function getTitle($object)
    {
        $title = '';
        // $title = str_replace(NnaTranslations::$cyrillicMap, NnaTranslations::$latinMap, $translator->translateString($object->objectTypes->type, $code));
        foreach (array('1','2','3','4','5','6') as $number) {
            if (!$object->{'level'.$number}) {
                break;
            }
            $title .= (!strlen($title) ? '' : ', ').$object->{'level'.$number};
        }
        if ($object->street) {
            $title .= (!strlen($title) ? '' : ', ').$object->street->_;
        }
        if ($object->houseNumber) {
            $title .= (!strlen($title) ? '' : ' ').$object->houseNumber->_;
        }
        return $title;
    }

    /**
     * array['ids']
     * array['images']
     *
     * @param $object
     * @return array|bool
     */
    public function insertObjectToWp($object)
    {
        $ids = array();
        $translator = NnaTranslations::getInstance();
        foreach (NnaApi::getInstance()->getLanguages() as $code) {
            // $title = $object->title;
            // if (!$title) {
            //     $title = $this->getTitle($object);
            // }
            $title = $this->getTitle($object);
            $content = '';
            if ($object->transactions->transaction->info){
                foreach ($object->transactions->transaction->info as $info){
                    if ($info->language == NnaApi::getInstance()->getLanguageCode(pll_current_language()) && $info->_){
                        $content = $info->_;
                        $patterns = array(
                            '---<br />
Pindi Kinnisvara on Eesti suurim kinnisvarabüroo, millel on 18 esindust üle Eesti. www.pindi.ee. Meie andmebaasis on üle 3000 aktiivse pakkumise (sh üle 600 äripinna) ning aastas hindame u 5000 kinnisvaraobjekti. Meie haldus-hooldusfirma portfellis on ligi 200 korterelamut ja ärihoonet, grupi kindlustusettevõttel on 5000 klienti.',
                            'Pindi Kinnisvara on Eesti suurim kinnisvarabüroo, millel on 18 esindust üle Eesti. www.pindi.ee. Meie andmebaasis on üle 3000 aktiivse pakkumise (sh üle 600 äripinna) ning aastas hindame u 5000 kinnisvaraobjekti. Meie haldus-hooldusfirma portfellis on ligi 200 korterelamut ja ärihoonet, grupi kindlustusettevõttel on 5000 klienti.',
                        );
                        foreach ($patterns as $pattern) {
                            $content = str_replace($pattern, '', $content);
                        }
                    }
                }
            }
            $postId = wp_insert_post(array(
                'post_title' => $title,
                'post_type' => NewtimeNextApi::$objectPostType,
                'post_status' => 'publish',
                'post_content' => $content,
            ), true);
            if (is_wp_error($postId)) {
                echo 'Failed to insert '.$object->originalId.PHP_EOL;
                foreach ($ids as $id) {
                    wp_delete_post($id, true);
                }
                print_r($postId);
                die();
                return false;
            }
            pll_set_post_language($postId, $code);
            $ids[$code] = $postId;
        }
        pll_save_post_translations($ids);
        $attachments = $this->insertImages($object->pictures->pictureUrl, $ids[pll_default_language()]);
        foreach ($ids as $id) {
            $this->populateImageFields($id, $attachments);
            $this->populateGoogleMapField($id, $object);
        }
        return array(
            'ids' => $ids,
            'images' => $attachments,
        );
    }

    public function populateImageFields($postId, $attachments)
    {
        $key = $this->getImagesKey();
        if (!$key) {
            return;
        }
        // $value = array_filter(get_field($key, $postId), function($item) {
        //     return isset($item['image']) && isset($item['image']['id']);
        // });
        // $oldImages = array_map(function($item) {
        //     return $item['image']['id'];
        // }, $value);
        $value = array();
        foreach ($attachments as $attachment) {
            $value[] = array('image' => $attachment->ID);
            // if (!in_array($attachment->ID, $oldImages)) {
            //     $value[] = array('image' => $attachment->ID);
            // }
        }
        update_field($key, $value, $postId);
    }

    public function populateGoogleMapField($id, $object)
    {
        $googleMapKey = $this->getGoogleMapKey();
        $value = get_field($googleMapKey, $id);
        
        $initialValue = array();
        $coords = CurlHandler::getInstance()->getCoordinates($object);

        if ($coords) {
            $initialValue['lat'] = $coords->lat;
            $initialValue['lng'] = $coords->lng;
            $initialValue['address'] = NnaApi::getInstance()->getAddress($object);
        }

        $match = true;
        foreach(['lat', 'lng', 'address'] as $key) {
            if (!$value || $value[$key] !== $initialValue[$key]) {
                $match = false;
            }
        }

        if (!empty($initialValue) && (empty($value) || !$match)) {
            //file_put_contents(NNA_PATH . '/log/debug.log', "Updating map for postid: {$id} - ".json_encode($initialValue).PHP_EOL, FILE_APPEND);
            if (!update_field($googleMapKey, $initialValue, $id)) {
                file_put_contents(NNA_PATH . '/log/debug.log', "Could not update map for postid: {$id}".PHP_EOL, FILE_APPEND);
            }
        }
    }

    public function formatImage($attachment)
    {
        // validate
        if( !$attachment )
        {
            return false;
        }

        // create array to hold value data
        $src = wp_get_attachment_image_src( $attachment->ID, 'full' );

        $value = array(
            'id' => $attachment->ID,
            'alt' => get_post_meta($attachment->ID, '_wp_attachment_image_alt', true),
            'title' => $attachment->post_title,
            'caption' => $attachment->post_excerpt,
            'description' => $attachment->post_content,
            'mime_type' => $attachment->post_mime_type,
            'url' => $src[0],
            'width' => $src[1],
            'height' => $src[2],
            'sizes' => array(),
        );


        // find all image sizes
        $image_sizes = get_intermediate_image_sizes();

        if( $image_sizes )
        {
            foreach( $image_sizes as $image_size )
            {
                // find src
                $src = wp_get_attachment_image_src( $attachment->ID, $image_size );

                // add src
                $value[ 'sizes' ][ $image_size ] = $src[0];
                $value[ 'sizes' ][ $image_size . '-width' ] = $src[1];
                $value[ 'sizes' ][ $image_size . '-height' ] = $src[2];
            }
        }

        return $value;
    }
}
