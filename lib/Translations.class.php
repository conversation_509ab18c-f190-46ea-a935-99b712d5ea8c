<?php
class NnaTranslations
{
    const PREFIX_FIELD = 'prefix';
    const STRING_FIELD = 'translation_string';

    public static $latinMap = array(
        "A","B","V","G","D","E","E","Z","Z","I","J","K","L","M","N","O","P","R","S","T","U","F","H","Ts","C","S","S","b","Y","b","E","U","A",'a','b','v','g','d','e','yo','z','z','i','j','k','l','m','n','o','p','r','s','t','u','f','h','t','c','s','s','b','y','b','e','u','a'
    );

    public static $cyrillicMap = array(
        'A','Б','В','Г','Д','Е','Ё','Ж','З','И','Й','К','Л','М','Н','О','П','Р','С','Т','У','Ф','Х','Ц','Ч','Ш','Щ','Ъ','Ы','Ь','Э','Ю','Я','a','б','в','г','д','е','ё','ж','з','и','й','к','л','м','н','о','п','р','с','т','у','ф','х','ц','ч','ш','щ','ъ','ы','ь','э','ю','я'
    );

    public static $languageCodes = array(
        'et' => 'est',
        'en' => 'eng',
        'ru' => 'rus',
        'fi' => 'fin',
    );

    private static $instance;
    private $_translated = array();

    public function getScoroCode($code)
    {
        if (isset(self::$languageCodes[$code])) {
            return self::$languageCodes[$code];
        }
        return $code;
    }

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

    public function __($string)
    {
        if (!function_exists('pll__')) {
            return $string;
        }
        $this->checkTranslation($string);
        return pll__($string);
    }

    public function _e($string)
    {
        echo $this->__($string);
    }

    public function translateString($string, $languageCode)
    {
        if (!function_exists('pll_translate_string')) {
            return $string;
        }
        $this->checkTranslation($string);
        return pll_translate_string($string, $languageCode);
    }

    public function checkTranslation($string, $prefix = '')
    {
        if (!in_array($string, $this->_translated)) {
            global $wpdb;
            $tableName = $wpdb->prefix . "nna_string_translations";
            $wpdb->insert($tableName, array(
                self::STRING_FIELD => $string,
                self::PREFIX_FIELD => $prefix
            ), '%s');
            $this->registerTranslations();
        }
    }

    public function registerTranslations()
    {
        if (!function_exists('pll_register_string')) {
            return;
        }
        global $wpdb;
        $tableName = $wpdb->prefix . "nna_string_translations";

        $translations = $wpdb->get_results("SELECT * FROM $tableName");

        $this->_translated = array();
        foreach ($translations as $translation) {
            $string = $translation->{SELF::STRING_FIELD};
            $prefix = $translation->{SELF::PREFIX_FIELD};
            if (!$prefix) {
                $prefix = 'Scoro';
            }
            pll_register_string($translation->{SELF::PREFIX_FIELD} . $string, $string, $prefix);
            $this->_translated[] = $string;
        }
    }
}
