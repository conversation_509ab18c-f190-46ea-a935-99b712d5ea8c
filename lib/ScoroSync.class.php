<?php
class ScoroSync extends Sync {

	private static $instance;

    public static function getInstance()
    {
        if (null == self::$instance) {
            self::$instance = new self;
        }

        return self::$instance;
    }

	public function getObjectsWsdl()
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['user']) || !isset($options['password'])) {
            throw new Exception('No username or password');
        }
        ini_set("soap.wsdl_cache_enabled", "0");
        $context = stream_context_create(array(
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ));
        return new RealEstateDataExchange(
            'https://versions.next.ee/_next_development/maintenance/RealEstate/REDE.wsdl',
            array(
                'login' => $options['user'],
                'password' => $options['password'],
                'trace' => 1,
                'stream_context' => $context,
            )
        );
    }

    public function getWsdlParams($request)
    {
        $options = get_option(NnaSettings::$connectionSettingsName);
        if (!isset($options['user']) || !isset($options['password'])) {
            throw new Exception('No username or password');
        }
        $params = new $request();
        $params->username = $options['user'];
        $params->password = $options['password'];
        return $params;
    }

    public function syncUsers()
    {
    	$api = NnaApi::getInstance();
        $client = $this->getObjectsWsdl();

        $params = $this->getWsdlParams('getBrokerIdsByStatus');
        $params->status = 'active';

        $data = $client->getBrokerIdsByStatus($params);

        $ids = $client->getBrokerIdsByStatus($params)->originalId;
        $oids = array();

        $condition=array(
            'Broker.originalId' => array(
                '$nin' => $ids,
            )
        );
        echo 'deleted '.$api->deleteBrokers($condition, false)->getDeletedCount().' records';

        $collection = $api->getCollection($api->getDbName().'.brokers');
        $sobject=array();
        $sobject['Broker.originalId']['$in']=$ids;
        foreach ($collection->find($sobject) as $object) {
            $id = $object->Broker->originalId;
            $oids[] = $id;

            $params = $this->getWsdlParams('getBrokerById');
            $params->originalId = $id;
            $rs = $client->getBrokerById($params);
            $rs->Broker->originalId = (int)$rs->Broker->originalId;

            echo 'updateing: #'.$object->Broker->originalId.' ... ';
            if ($collection->updateOne(array('_id' => $object->_id), array('$set' => $rs))) {
                WpHandler::getInstance()->updateBrokerPosts($object, $rs);
                echo 'updated '.$id;
            }
            echo PHP_EOL;
        }

        foreach ($ids as $id) {
            if(!in_array($id,$oids)){
                echo 'Insert item #'.$id;

                $params = $this->getWsdlParams('getBrokerById');
                $params->originalId = $id;
                $broker = $client->getBrokerById($params);
                $broker->Broker->originalId = (int)$broker->Broker->originalId;

                $postIds = WpHandler::getInstance()->insertBrokerToWp($broker->Broker);
                if ($postIds) {
                    $collection->insertOne(array(
                        'PostIds' => $postIds,
                        'Broker' => $broker->Broker,
                        'Metainfo' => $broker->Metainfo,
                    ));
                    echo ' inserted';
                }
                echo PHP_EOL;
            }
        }
    }

    public function startObjectSync($all = false)
    {
        ini_set("memory_limit","1024M");
        file_put_contents(NNA_PATH . '/log/synclog.log', date('d-m-Y H:i-s').PHP_EOL, FILE_APPEND);

        $api = NnaApi::getInstance();
        $client = $this->getObjectsWsdl();

        $params = $this->getWsdlParams('getObjectIdsByStatus');
        $params->status = 'active';

        $scoroIds = $ids = $client->getObjectIdsByStatus($params)->originalId;
        echo 'total objects: '.count($ids).PHP_EOL;

        $condition=array(
            'Object.originalId' => array(
                '$nin' => $ids,
            ),
            'Metainfo.source' => 'Next',
        );

        $deleted = $api->deleteObjects($condition, false)->getDeletedCount();
        echo 'deleted: '.$deleted.PHP_EOL;
        // die();
        file_put_contents(NNA_PATH . '/log/synclog.log', 'deleted '.$deleted.' records', FILE_APPEND);
        // $testId = 472133;
        // $testId = 471223;

        if (!$all) {
            $params = $this->getWsdlParams('getObjectIdsModifiedAfter');
            $ids = $client->getObjectIdsModifiedAfter($params)->originalId;
            // print_r($ids);
            echo count($ids);
            // var_dump(in_array($testId, $ids));
        }

        // $this->updateObjects([$testId], $client);

        // die();

        if (!is_array($ids)) {
            $ids = array(
                $ids,
            );
        }

        $forInsert = $this->updateObjects($ids, $client);

        $mongoIds = array_map(
            function($item) {
                return $item->Object->originalId;
            },
            $api->getObjects(
            	array(
            		'Metainfo.source' => 'Next',
            	),
            	array(
	                'projection' => array(
	                    'Object.originalId' => true,
	                )
	            )
        	)
        );
        $forInsert = array_diff($scoroIds, $mongoIds);

        print_r($forInsert);
        $this->insertObjects($forInsert, $client);
    }

    public function updateObjects($ids, $client)
    {
    	echo 'update objects';
    	$api = NnaApi::getInstance();
        $existing = array();
        $collection = $api->getCollection($api->getDbName().'.objects');
        $sobject=array();
        $sobject['Object.originalId']['$in']=$ids;
        foreach ($collection->find($sobject, array('noCursorTimeout' => true)) as $object) {
            echo '.';
            $id = $object->Object->originalId;
            $existing[] = $id;

            $params = $this->getWsdlParams('getObjectById');
            $params->originalId = $id;
            try {
                $rs = $client->getObjectById($params);
            } catch (SoapFault $e) {
                $client = $this->getObjectsWsdl();
                $rs = $client->getObjectById($params);
            }

            file_put_contents(NNA_PATH . '/log/synclog.log', 'checking: #'.$rs->Object->originalId.PHP_EOL, FILE_APPEND);

            $ours=new DateTime($object->Object->lastModified);
            $their=new DateTime($rs->Object->lastModified);
            if($their>$ours){
            	$object->Object = $this->fixObject($object->Object);
                $this->updateObject($rs, $object, $collection);
                $params = $this->getWsdlParams('objectUpdateCompleted');
                $params->Objects = $rs;
                $response = $client->objectUpdateCompleted($params);
                // echo 'updated';
                // print_r($params);
            }
        }
        return array_diff($ids, $existing);
    }

    public function insertObjects($ids, $client)
    {
    	$api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName().'.objects');
        foreach ($ids as $id) {
            echo '.';
            file_put_contents(NNA_PATH . '/log/synclog.log', 'Insert item #'.$id.PHP_EOL, FILE_APPEND);
            $params = $this->getWsdlParams('getObjectById');
            $params->originalId = $id;
            try {
                $object = $client->getObjectById($params);
            } catch (SoapFault $e) {
                $client = $this->getObjectsWsdl();
                $object = $client->getObjectById($params);
            }
            $object->Object = $this->fixObject($object->Object);
            $this->insertObject($object, $collection);
        }
    }

    protected function fixObject($object) {
    	// print_r($object->level1);
    	$object->level1 = $this->getCountyNameMapper()->mapName($object->level1);
    	$object->level2 = $this->getCityNameMapper()->mapName($object->level2);

    	if ($this->hasGasSupply($object)) {
    		$object->gasSupply = 'CUSTOM_FIELD_OBJECT_GAS_SUPPLY_AVAILABLE';
    	}

        $object->coldWater = $this->getWaterTypes($object);

    	return $object;
    }

    protected function hasGasSupply($object) {
    	if ($object->options && is_array($object->options->option)) {
	    	foreach ($object->options->option as $option) {
	    		if ($option->_ == 'gas') {
	    			return true;
	    		}
	    	}
    	}
    	return false;
    }

    protected function getWaterTypes($object) {
        $waterTypes = [];
        if ($object->options && is_array($object->options->option)) {
            foreach ($object->options->option as $option) {
                if ($option->_ == 'localwater') {
                    $waterTypes[] = 'localwater';
                } else if ($option->_ == 'urbanwater') {
                    $waterTypes[] = 'CUSTOM_FIELD_OBJECT_COLD_WATER_CENTRAL';
                }
            }
        }
        return $waterTypes;
    }

    public function fixObjectFields($condition = []) {
    	$api = NnaApi::getInstance();
        $collection = $api->getCollection($api->getDbName().'.objects');
        foreach ($collection->find($condition, array('noCursorTimeout' => true)) as $object) {
        	$fixedObject = $this->fixObject($object->Object);
	    	$collection->updateOne(
	    		array('_id' => $object->_id),
	    		array(
		            '$set' => array(
		                'Object' => $fixedObject,
		            )
		        )
			);
        }
    }

    public function test()
    {
    	// $client = $this->getObjectsWsdl();
    	// // $id = 472133;
    	// // $id = 307855;
    	// // $id = 471553;
    	// $id = 185298;
    	// $params = $this->getWsdlParams('getObjectById');
     //    $params->originalId = $id;
     //    $object = $client->getObjectById($params);
     //    $object->Object = $this->fixObject($object->Object);
     //    // $their=new DateTime($object->Object->lastModified);
     //    // print_r($object->Object->lastModified);
     //    // print_r($their);
     //    print_r($object);
    	$api = NnaApi::getInstance();
    	var_dump($api->getObjectsCount(['Object.objectTypes.type' => 'OBJECT_TYPE_KASI_STORE']));
    	$object = $api->getObject(['Object.objectTypes.type' => 'OBJECT_TYPE_KASI_STORE']);
    	print_r($object);
  //       // $collection = $api->getCollection($api->getDbName().'.objects');
  //       $collection = $api->getCollection($api->getDbName().'.objects');
  //       $collection->updateOne(
  //   		array('_id' => $object->_id),
  //   		array(
	 //            '$set' => array(
	 //                'Object.transactions.transaction.type' => 'rent',
	 //            )
	 //        )
		// );
	}
}
