<?php
if( php_sapi_name() !== 'cli' ) {
    die("Meant to be run from command line");
}

function find_wordpress_base_path() {
    $dir = dirname(__FILE__);
    do {
        //it is possible to check for other files here
        if( file_exists($dir."/wp-config.php") ) {
            return $dir;
        }
    } while( $dir = realpath("$dir/..") );
    return null;
}

define('BASE_PATH', '/Users/<USER>/server/pindiline/');
define('WP_USE_THEMES', false);
global $wp, $wp_query, $wp_the_query, $wp_rewrite, $wp_did_header;
require(BASE_PATH . 'wp-load.php');

// Test the API connection
try {
    $brenollis = BrenollisSync::getInstance();
    
    // Get a single advert
    $params = ['size' => 1, 'page' => 0, 'sortField' => 'id', 'sortDirection' => 'ASC'];
    $queryResponse = $brenollis->getAdverts($params);
    
    echo "API connection successful!\n";
    echo "Total pages: " . $queryResponse->totalPages . "\n";
    echo "Total elements: " . $queryResponse->totalElements . "\n";
    
    if (isset($queryResponse->content) && count($queryResponse->content) > 0) {
        $advert = $queryResponse->content[0];
        echo "First advert ID: " . $advert->id . "\n";
        echo "First advert status: " . $advert->status . "\n";
    } else {
        echo "No adverts found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo 'updated: ' . $count . ' inserted: ' . $response['inserted'] . ' page: ' . $page . PHP_EOL;
